<svg fill="none" height="200" viewBox="0 0 200 200" width="200" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><filter id="a" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse" height="101.998" width="140" x="38" y="53"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feColorMatrix in="SourceAlpha" result="hardAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dx="8" dy="8"/><feGaussianBlur stdDeviation="8"/><feColorMatrix type="matrix" values="0 0 0 0 0.772549 0 0 0 0 0.792157 0 0 0 0 0.819608 0 0 0 0.16 0"/><feBlend in2="BackgroundImageFix" mode="normal" result="effect1_dropShadow_1587_120560"/><feBlend in="SourceGraphic" in2="effect1_dropShadow_1587_120560" mode="normal" result="shape"/><feColorMatrix in="SourceAlpha" result="hardAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dx="-2" dy="-2"/><feGaussianBlur stdDeviation="2"/><feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/><feColorMatrix type="matrix" values="0 0 0 0 0.772549 0 0 0 0 0.792157 0 0 0 0 0.819608 0 0 0 0.48 0"/><feBlend in2="shape" mode="normal" result="effect2_innerShadow_1587_120560"/></filter><filter id="b" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse" height="89.9353" width="84.4639" x="65.3706" y="89.0818"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feColorMatrix in="SourceAlpha" result="hardAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dx="8" dy="8"/><feGaussianBlur stdDeviation="8"/><feColorMatrix type="matrix" values="0 0 0 0 0.717647 0 0 0 0 0.431373 0 0 0 0 0 0 0 0 0.16 0"/><feBlend in2="BackgroundImageFix" mode="normal" result="effect1_dropShadow_1587_120560"/><feBlend in="SourceGraphic" in2="effect1_dropShadow_1587_120560" mode="normal" result="shape"/><feColorMatrix in="SourceAlpha" result="hardAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dx="-2" dy="-2"/><feGaussianBlur stdDeviation="2"/><feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/><feColorMatrix type="matrix" values="0 0 0 0 0.717647 0 0 0 0 0.431373 0 0 0 0 0 0 0 0 0.48 0"/><feBlend in2="shape" mode="normal" result="effect2_innerShadow_1587_120560"/></filter><g filter="url(#a)"><path d="m118.897 127.097c-3.582.173-2.407 3.901 1.178 3.901h11.566c12.329 0 22.359-10.026 22.359-22.349 0-9.4883-5.946-17.6145-14.309-20.8511-4.106-1.5887-7.97-4.1414-11.026-7.3094-4.121-4.272-9.863-6.8383-16.009-6.8383-.994 0-1.946-.4471-2.546-1.2399-5.388-7.1167-13.8132-11.4103-22.7662-11.4103-12.9686 0-23.9548 8.6448-27.4924 20.4723-1.2747 4.2616-3.8863 8.1418-7.0762 11.2419-4.2339 4.1149-6.7752 9.8258-6.7752 15.9348 0 12.323 10.0303 22.349 22.3594 22.349h18.9844c.296 0 .4183-.34.2148-.555-1.8513-1.957-4.4482-3.21-7.3177-3.349-.4511-.022-.8859-.045-1.3047-.068-5.1641-.282-7.3133-5.192-3.941-9.113 1.9769-2.299 4.529-5.12 7.7941-8.504 5.671-5.878 9.9072-9.208 12.7091-11.0631 2.5465-1.686 5.6636-1.6863 8.2106-.0003 2.802 1.8554 7.038 5.1844 12.709 11.0634 3.263 3.383 5.814 6.203 7.791 8.502 3.373 3.922 1.224 8.834-3.942 9.115-.439.024-.895.048-1.37.071z" fill="#fff"/></g><g filter="url(#b)"><path clip-rule="evenodd" d="m118.897 127.097c-5.333.258-9.725 4.363-10.14 9.687l-.918 11.786c-.242 3.111-1.744 5.893-4.842 6.263-.943.113-2.074.184-3.4284.184-1.3539 0-2.4851-.071-3.428-.183-3.0987-.371-4.6007-3.153-4.8432-6.264l-.9181-11.789c-.4145-5.323-4.8055-9.428-10.1382-9.687-.4511-.022-.8859-.044-1.3047-.067-5.1641-.282-7.3133-5.192-3.941-9.113 1.9769-2.299 4.529-5.121 7.7941-8.505 5.671-5.878 9.9072-9.207 12.7091-11.0626 2.5465-1.686 5.6634-1.6862 8.2104-.0002 2.802 1.8548 7.038 5.1848 12.709 11.0628 3.263 3.383 5.815 6.204 7.791 8.502 3.373 3.923 1.224 8.834-3.941 9.116-.44.024-.896.047-1.371.07z" fill="#ffab00" fill-rule="evenodd"/></g></svg>