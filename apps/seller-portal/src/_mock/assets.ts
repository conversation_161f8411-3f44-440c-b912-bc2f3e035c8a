// ----------------------------------------------------------------------

export const _id = [...Array(40)].map(
  (_, index) => `e99f09a7-dd88-49d5-b1c8-1daf80c2d7b${index + 1}`
);

// ----------------------------------------------------------------------

export const _booleans = [
  true,
  true,
  true,
  false,
  false,
  true,
  false,
  false,
  false,
  false,
  true,
  true,
  true,
  false,
  false,
  false,
  true,
  false,
  false,
  false,
  true,
  false,
  false,
  true,
];

// ----------------------------------------------------------------------

export const _prices = [
  83.74, 97.14, 68.71, 85.21, 52.17, 25.18, 43.84, 60.98, 98.42, 53.37, 72.75, 56.61, 64.55, 77.32,
  60.62, 79.81, 93.68, 47.44, 76.24, 92.87, 72.91, 20.54, 94.25, 37.51,
];

export const _ratings = [
  4.2, 3.7, 4.5, 3.5, 0.5, 3.0, 2.5, 2.8, 4.9, 3.6, 2.5, 1.7, 3.9, 2.8, 4.1, 4.5, 2.2, 3.2, 0.6,
  1.3, 3.8, 3.8, 3.8, 2.0,
];

export const _ages = [
  30, 26, 59, 47, 29, 46, 18, 56, 39, 19, 45, 18, 46, 56, 38, 41, 44, 48, 32, 45, 42, 60, 33, 57,
];

export const _percents = [
  10.1, 13.6, 28.2, 42.1, 37.2, 18.5, 40.1, 94.8, 91.4, 53.0, 25.4, 62.9, 86.6, 62.4, 35.4, 17.6,
  52.0, 6.8, 95.3, 26.6, 69.9, 92.1, 46.2, 85.6,
];

export const _nativeS = [
  11, 10, 7, 10, 12, 5, 10, 1, 8, 8, 10, 11, 12, 8, 4, 11, 8, 9, 4, 9, 2, 6, 3, 7,
];

export const _nativeM = [
  497, 763, 684, 451, 433, 463, 951, 194, 425, 435, 807, 521, 538, 839, 394, 269, 453, 821, 364,
  849, 804, 776, 263, 239,
];

export const _nativeL = [
  9911, 1947, 9124, 6984, 8488, 2034, 3364, 8401, 8996, 5271, 8478, 1139, 8061, 3035, 6733, 3952,
  2405, 3127, 6843, 4672, 6995, 6053, 5192, 9686,
];

export const _fullAddress = [
  `19034 Verna Unions Apt. 164 - Honolulu, RI / 87535`,
  `1147 Rohan Drive Suite 819 - Burlington, VT / 82021`,
  `18605 Thompson Circle Apt. 086 - Idaho Falls, WV / 50337`,
  `110 Lamar Station Apt. 730 - Hagerstown, OK / 49808`,
  `36901 Elmer Spurs Apt. 762 - Miramar, DE / 92836`,
  `2089 Runolfsson Harbors Suite 886 - Chapel Hill, TX / 32827`,
  `279 Karolann Ports Apt. 774 - Prescott Valley, WV / 53905`,
  `96607 Claire Square Suite 591 - St. Louis Park, HI / 40802`,
  `9388 Auer Station Suite 573 - Honolulu, AK / 98024`,
  `47665 Adaline Squares Suite 510 - Blacksburg, NE / 53515`,
  `989 Vernice Flats Apt. 183 - Billings, NV / 04147`,
  `91020 Wehner Locks Apt. 673 - Albany, WY / 68763`,
  `585 Candelario Pass Suite 090 - Columbus, LA / 25376`,
  `80988 Renner Crest Apt. 000 - Fargo, VA / 24266`,
  `28307 Shayne Pike Suite 523 - North Las Vegas, AZ / 28550`,
  `205 Farrell Highway Suite 333 - Rock Hill, OK / 63421`,
  `253 Kara Motorway Suite 821 - Manchester, SD / 09331`,
  `13663 Kiara Oval Suite 606 - Missoula, AR / 44478`,
  `8110 Claire Port Apt. 703 - Anchorage, TN / 01753`,
  `4642 Demetris Lane Suite 407 - Edmond, AZ / 60888`,
  `74794 Asha Flat Suite 890 - Lancaster, OR / 13466`,
  `8135 Keeling Pines Apt. 326 - Alexandria, MA / 89442`,
  `441 Gibson Shores Suite 247 - Pasco, NM / 60678`,
  `4373 Emelia Valley Suite 596 - Columbia, NM / 42586`,
];

// ----------------------------------------------------------------------

export const _emails = [
  `<EMAIL>`,
  `<EMAIL>`,
  `<EMAIL>`,
  `<EMAIL>`,
  `<EMAIL>`,
  `<EMAIL>`,
  `<EMAIL>`,
  `<EMAIL>`,
  `<EMAIL>`,
  `<EMAIL>`,
  `<EMAIL>`,
  `<EMAIL>`,
  `<EMAIL>`,
  `<EMAIL>`,
  `<EMAIL>`,
  `<EMAIL>`,
  `<EMAIL>`,
  `<EMAIL>`,
  `<EMAIL>`,
  `<EMAIL>`,
  `<EMAIL>`,
  `<EMAIL>`,
  `<EMAIL>`,
  `<EMAIL>`,
];

// ----------------------------------------------------------------------

export const _fullNames = [
  `Jayvion Simon`,
  `Lucian Obrien`,
  `Deja Brady`,
  `Harrison Stein`,
  `Reece Chung`,
  `Lainey Davidson`,
  `Cristopher Cardenas`,
  `Melanie Noble`,
  `Chase Day`,
  `Shawn Manning`,
  `Soren Durham`,
  `Cortez Herring`,
  `Brycen Jimenez`,
  `Giana Brandt`,
  `Aspen Schmitt`,
  `Colten Aguilar`,
  `Angelique Morse`,
  `Selina Boyer`,
  `Lawson Bass`,
  `Ariana Lang`,
  `Amiah Pruitt`,
  `Harold Mcgrath`,
  `Esperanza Mcintyre`,
  `Mireya Conner`,
];

export const _firstNames = [
  `Mossie`,
  `David`,
  `Ebba`,
  `Chester`,
  `Eula`,
  `Jaren`,
  `Boyd`,
  `Brady`,
  `Aida`,
  `Anastasia`,
  `Gregoria`,
  `Julianne`,
  `Ila`,
  `Elyssa`,
  `Lucio`,
  `Lewis`,
  `Jacinthe`,
  `Molly`,
  `Brown`,
  `Fritz`,
  `Keon`,
  `Ella`,
  `Ken`,
  `Whitney`,
];

export const _lastNames = [
  `Carroll`,
  `Simonis`,
  `Yost`,
  `Hand`,
  `Emmerich`,
  `Wilderman`,
  `Howell`,
  `Sporer`,
  `Boehm`,
  `Morar`,
  `Koch`,
  `Reynolds`,
  `Padberg`,
  `Watsica`,
  `Upton`,
  `Yundt`,
  `Pfeffer`,
  `Parker`,
  `Zulauf`,
  `Treutel`,
  `McDermott`,
  `McDermott`,
  `Cruickshank`,
  `Parisian`,
];

// ----------------------------------------------------------------------

export const _phoneNumbers = [
  '******-555-0143',
  '******-555-0198',
  '+44 20 7946 0958',
  '+61 2 9876 5432',
  '+91 22 1234 5678',
  '+49 30 123456',
  '+33 1 23456789',
  '+81 3 1234 5678',
  '+86 10 1234 5678',
  '+55 11 2345-6789',
  '+27 11 123 4567',
  '****** 123-4567',
  '+52 55 1234 5678',
  '+39 06 123 4567',
  '+34 91 123 4567',
  '+31 20 123 4567',
  '+46 8 123 456',
  '+41 22 123 45 67',
  '+82 2 123 4567',
  '+54 11 1234-5678',
  '+64 9 123 4567',
  '+65 1234 5678',
  '+60 3-1234 5678',
  '+66 2 123 4567',
  '+62 21 123 4567',
  '+63 2 123 4567',
  '+90 212 123 45 67',
  '+966 11 123 4567',
  '+971 2 123 4567',
  '+20 2 12345678',
  '+234 1 123 4567',
  '+254 20 123 4567',
  '+972 3-123-4567',
  '+30 21 1234 5678',
  '+353 1 123 4567',
  '+351 21 123 4567',
  '+47 21 23 45 67',
  '+45 32 12 34 56',
  '+358 9 123 4567',
  '+48 22 123 45 67',
];

// ----------------------------------------------------------------------

export const _countryNames = [
  'United States',
  'Canada',
  'United Kingdom',
  'Australia',
  'India',
  'Germany',
  'France',
  'Japan',
  'China',
  'Brazil',
  'South Africa',
  'Russia',
  'Mexico',
  'Italy',
  'Spain',
  'Netherlands',
  'Sweden',
  'Switzerland',
  'South Korea',
  'Argentina',
  'New Zealand',
  'Singapore',
  'Malaysia',
  'Thailand',
  'Indonesia',
  'Philippines',
  'Turkey',
  'Saudi Arabia',
  'United Arab Emirates',
  'Egypt',
  'Nigeria',
  'Kenya',
  'Israel',
  'Greece',
  'Ireland',
  'Portugal',
  'Norway',
  'Denmark',
  'Finland',
  'Poland',
];

// ----------------------------------------------------------------------

export const _roles = [
  `CEO`,
  `CTO`,
  `Project Coordinator`,
  `Team Leader`,
  `Software Developer`,
  `Marketing Strategist`,
  `Data Analyst`,
  `Product Owner`,
  `Graphic Designer`,
  `Operations Manager`,
  `Customer Support Specialist`,
  `Sales Manager`,
  `HR Recruiter`,
  `Business Consultant`,
  `Financial Planner`,
  `Network Engineer`,
  `Content Creator`,
  `Quality Assurance Tester`,
  `Public Relations Officer`,
  `IT Administrator`,
  `Compliance Officer`,
  `Event Planner`,
  `Legal Counsel`,
  `Training Coordinator`,
];

// ----------------------------------------------------------------------

export const _postTitles = [
  `Product Name 1`,
  `Product Name 2`,
  `Product Name 3`,
  `Product Name 4`,
  `Product Name 5`,
  `Mental Health in the Digital Age: Navigating Social Media and Well-being`,
  `Sustainable Fashion: How the Industry is Going Green`,
  `Space Exploration: New Frontiers and the Quest for Extraterrestrial Life`,
  `The Evolution of E-Commerce: Trends Shaping the Online Retail Landscape`,
  `Cybersecurity in the 21st Century: Protecting Data in a Digital World`,
  `The Role of Big Data in Transforming Business Strategies`,
  `Genetic Engineering: Ethical Considerations and Future Prospects`,
  `Urban Farming: A Solution to Food Deserts and Urban Sustainability`,
  `The Psychology of Consumer Behavior: What Drives Our Purchasing Decisions?`,
  `Renewable Energy vs. Fossil Fuels: Which is the Future?`,
  `Artificial Intelligence in Education: Enhancing Learning Experiences`,
  `The Impact of Climate Change on Global Migration Patterns`,
  `5G Technology: Revolutionizing Connectivity and Communication`,
  `The Gig Economy: Opportunities, Risks, and the Future of Work`,
  `Smart Cities: Integrating Technology for Sustainable Urban Living`,
  `The Influence of Pop Culture on Modern Society`,
  `Innovations in Medicine: From Telehealth to Personalized Treatment`,
  `The Environmental Cost of Fast Fashion: What Can Consumers Do?`,
  `The Intersection of Art and Technology: Digital Art in the 21st Century`,
];

// ----------------------------------------------------------------------

export const _productNames = [
  `Urban Explorer Sneakers`,
  `Classic Leather Loafers`,
  `Mountain Trekking Boots`,
  `Elegance Stiletto Heels`,
  `Comfy Running Shoes`,
  `Chic Ballet Flats`,
  `Vintage Oxford Shoes`,
  `Waterproof Hiking Boots`,
  `Casual Slip-On Sneakers`,
  `Premium Dress Shoes`,
  `Sporty Trail Runners`,
  `Sophisticated Brogues`,
  `Beach Sandals`,
  `Stylish Wedge Heels`,
  `Lightweight Training Shoes`,
  `Luxurious Moccasins`,
  `Durable Work Boots`,
  `Trendy Platform Sneakers`,
  `Cozy Winter Boots`,
  `Fashion Ankle Boots`,
  `Breathable Tennis Shoes`,
  `Elegant Evening Pumps`,
  `Modern Skate Shoes`,
  `Comfortable Walking Shoes`,
];

// ----------------------------------------------------------------------

export const _tourNames = [
  `Majestic Mountain Adventures`,
  `Island Hopping Extravaganza`,
  `Cultural Wonders of Europe`,
  `Safari Expedition in Africa`,
  `Grand Canyon Explorer`,
  `Historic Cities of Asia`,
  `Tropical Paradise Getaway`,
  `Alaskan Wilderness Tour`,
  `Mediterranean Cruise Voyage`,
  `Enchanting Eastern Europe`,
  `Scenic Coastal Road Trip`,
  `Ancient Ruins Discovery`,
  `Australian Outback Adventure`,
  `Northern Lights Experience`,
  `Wildlife Wonders of South America`,
  `Royal Castles and Palaces`,
  `Ultimate Beach Retreat`,
  `National Parks Exploration`,
  `Gastronomic Tour of Italy`,
  `Hiking Trails of New Zealand`,
  `Art and History of France`,
  `Exotic Temples of India`,
  `Canadian Rockies Journey`,
  `Caribbean Sun and Fun`,
];

// ----------------------------------------------------------------------

export const _jobTitles = [
  `Software Engineer`,
  `Marketing Manager`,
  `Data Scientist`,
  `Graphic Designer`,
  `Financial Analyst`,
  `Human Resources Specialist`,
  `Project Manager`,
  `Sales Executive`,
  `Content Writer`,
  `Network Administrator`,
  `Customer Service Representative`,
  `Product Manager`,
  `Business Analyst`,
  `Mechanical Engineer`,
  `Operations Manager`,
  `UX/UI Designer`,
  `Accountant`,
  `Social Media Manager`,
  `Research Scientist`,
  `Legal Advisor`,
  `Public Relations Specialist`,
  `Health and Safety Officer`,
  `IT Support Specialist`,
  `Environmental Consultant`,
];

// ----------------------------------------------------------------------

export const _companyNames = [
  `Lueilwitz and Sons`,
  `Gleichner, Mueller and Tromp`,
  `Nikolaus - Leuschke`,
  `Hegmann, Kreiger and Bayer`,
  `Grimes Inc`,
  `Durgan - Murazik`,
  `Altenwerth, Medhurst and Roberts`,
  `Raynor Group`,
  `Mraz, Donnelly and Collins`,
  `Padberg - Bailey`,
  `Heidenreich, Stokes and Parker`,
  `Pagac and Sons`,
  `Rempel, Hand and Herzog`,
  `Dare - Treutel`,
  `Kihn, Marquardt and Crist`,
  `Nolan - Kunde`,
  `Wuckert Inc`,
  `Dibbert Inc`,
  `Goyette and Sons`,
  `Feest Group`,
  `Bosco and Sons`,
  `Bartell - Kovacek`,
  `Schimmel - Raynor`,
  `Tremblay LLC`,
];

// ----------------------------------------------------------------------

export const _tags = [
  `Technology`,
  `Health and Wellness`,
  `Travel`,
  `Finance`,
  `Education`,
  `Food and Beverage`,
  `Fashion`,
  `Home and Garden`,
  `Sports`,
  `Entertainment`,
  `Business`,
  `Science`,
  `Automotive`,
  `Beauty`,
  `Fitness`,
  `Lifestyle`,
  `Real Estate`,
  `Parenting`,
  `Pet Care`,
  `Environmental`,
  `DIY and Crafts`,
  `Gaming`,
  `Photography`,
  `Music`,
];

// ----------------------------------------------------------------------

export const _taskNames = [
  `Prepare Monthly Financial Report`,
  `Design New Marketing Campaign`,
  `Analyze Customer Feedback`,
  `Update Website Content`,
  `Conduct Market Research`,
  `Develop Software Application`,
  `Organize Team Meeting`,
  `Create Social Media Posts`,
  `Review Project Plan`,
  `Implement Security Protocols`,
  `Write Technical Documentation`,
  `Test New Product Features`,
  `Manage Client Inquiries`,
  `Train New Employees`,
  `Coordinate Logistics`,
  `Monitor Network Performance`,
  `Develop Training Materials`,
  `Draft Press Release`,
  `Prepare Budget Proposal`,
  `Evaluate Vendor Proposals`,
  `Perform Data Analysis`,
  `Conduct Quality Assurance`,
  `Plan Event Logistics`,
  `Optimize SEO Strategies`,
];

// ----------------------------------------------------------------------

export const _courseNames = [
  `Introduction to Python Programming`,
  `Digital Marketing Fundamentals`,
  `Data Science with R`,
  `Graphic Design Essentials`,
  `Financial Planning for Beginners`,
  `Human Resource Management Basics`,
  `Project Management Fundamentals`,
  `Sales Techniques and Strategies`,
  `Content Writing Mastery`,
  `Network Security Fundamentals`,
  `Customer Service Excellence`,
  `Product Management Essentials`,
  `Business Analytics with Excel`,
  `Mechanical Engineering Principles`,
  `Leadership and Team Management`,
  `User Experience (UX) Design Basics`,
  `Accounting Fundamentals`,
  `Social Media Marketing Mastery`,
  `Biotechnology Essentials`,
  `Legal Studies for Non-Lawyers`,
  `Public Speaking Confidence`,
  `Health and Wellness Coaching`,
  `Web Development Bootcamp`,
  `Photography Masterclass`,
];

// ----------------------------------------------------------------------

export const _fileNames = [
  'cover-2.jpg',
  'design-suriname-2015.mp3',
  'expertise-2015-conakry-sao-tome-and-principe-gender.mp4',
  'money-popup-crack.pdf',
  'cover-4.jpg',
  'cover-6.jpg',
  'large-news.txt',
  'nauru-6015-small-fighter-left-gender.psd',
  'tv-xs.doc',
  'gustavia-entertainment-productivity.docx',
  'vintage-bahrain-saipan.xls',
  'indonesia-quito-nancy-grace-left-glad.xlsx',
  'legislation-grain.zip',
  'large-energy-dry-philippines.rar',
  'footer-243-ecuador.iso',
  'kyrgyzstan-********-picabo-street-guide-style.ai',
  'india-data-large-gk-chesterton-mother.esp',
  'footer-barbados-celine-dion.ppt',
  'socio-respectively-366996.pptx',
  'socio-ahead-531437-sweden-popup.wav',
  'trinidad-samuel-morse-bring.m4v',
  'cover-12.jpg',
  'cover-18.jpg',
  'xl-david-blaine-component-tanzania-books.pdf',
];

export const _eventNames = [
  `Annual General Meeting`,
  `Summer Music Festival`,
  `Tech Innovators Conference`,
  `Charity Gala Dinner`,
  `Spring Art Exhibition`,
  `Corporate Training Workshop`,
  `Community Health Fair`,
  `Startup Pitch Night`,
  `Regional Sports Tournament`,
  `Book Launch Event`,
  `Film Premiere Screening`,
  `Industry Networking Mixer`,
  `Holiday Craft Fair`,
  `Environmental Awareness Week`,
  `New Year's Eve Party`,
  `Product Release Showcase`,
  `Cultural Heritage Festival`,
  `Science and Technology Expo`,
  `Annual Awards Ceremony`,
  `Fashion Week Runway Show`,
  `Food and Wine Tasting`,
  `Outdoor Adventure Camp`,
  `Leadership Summit`,
  `Wedding Expo`,
];

// ----------------------------------------------------------------------

export const _sentences = [
  `SKU #582751928`,
  `SKU #**********`,
  `SKU #58275192311`,
  `SKU #582751928`,
  `SKU #582751922e`,
  `He carefully crafted a beautiful sculpture out of clay, his hands skillfully shaping the intricate details.`,
  `The concert was a mesmerizing experience, with the music filling the venue and the crowd cheering in delight.`,
  `The waves crashed against the shore, creating a soothing symphony of sound.`,
  `The scent of blooming flowers wafted through the garden, creating a fragrant paradise.`,
  `She gazed up at the night sky, marveling at the twinkling stars that dotted the darkness.`,
  `The professor delivered a captivating lecture, engaging the students with thought-provoking ideas.`,
  `The hiker trekked through the dense forest, guided by the soft glow of sunlight filtering through the trees.`,
  `The delicate butterfly gracefully fluttered from flower to flower, sipping nectar with its slender proboscis.`,
  `The aroma of freshly baked cookies filled the kitchen, tempting everyone with its irresistible scent.`,
  'The majestic waterfall cascaded down the rocks, creating a breathtaking display of nature`s power.',
  `The actor delivered a powerful performance, moving the audience to tears with his emotional portrayal.`,
  `The book transported me to a magical world, where imagination knew no bounds.`,
  `The scent of rain filled the air as dark clouds gathered overhead, promising a refreshing downpour.`,
  `The chef skillfully plated the dish, turning simple ingredients into a work of culinary art.`,
  `The newborn baby let out a tiny cry, announcing its arrival to the world.`,
  `The athlete sprinted across the finish line, arms raised in victory as the crowd erupted in applause.`,
  `The ancient ruins stood as a testament to a civilization long gone, their grandeur still awe-inspiring.`,
  `The artist dipped the brush into vibrant paint, bringing the canvas to life with bold strokes and vivid colors.`,
  `The laughter of children echoed through the playground, filling the atmosphere with pure joy.`,
];

// ----------------------------------------------------------------------

export const _descriptions = [
  `Occaecati est et illo quibusdam accusamus qui. Incidunt aut et molestiae ut facere aut. Est quidem iusto praesentium excepturi harum nihil tenetur facilis. Ut omnis voluptates nihil accusantium doloribus eaque debitis.`,
  `Atque eaque ducimus minima distinctio velit. Laborum et veniam officiis. Delectus ex saepe hic id laboriosam officia. Odit nostrum qui illum saepe debitis ullam. Laudantium beatae modi fugit ut. Dolores consequatur beatae nihil voluptates rem maiores.`,
  `Rerum eius velit dolores. Explicabo ad nemo quibusdam. Voluptatem eum suscipit et ipsum et consequatur aperiam quia. Rerum nulla sequi recusandae illum velit quia quas. Et error laborum maiores cupiditate occaecati.`,
  `Et non omnis qui. Qui sunt deserunt dolorem aut velit cumque adipisci aut enim. Nihil quis quisquam nesciunt dicta nobis ab aperiam dolorem repellat. Voluptates non blanditiis. Error et tenetur iste soluta cupiditate ratione perspiciatis et. Quibusdam aliquid nam sunt et quisquam non esse.`,
  `Nihil ea sunt facilis praesentium atque. Ab animi alias sequi molestias aut velit ea. Sed possimus eos. Et est aliquid est voluptatem.`,
  `Non rerum modi. Accusamus voluptatem odit nihil in. Quidem et iusto numquam veniam culpa aperiam odio aut enim. Quae vel dolores. Pariatur est culpa veritatis aut dolorem.`,
  `Est enim et sit non impedit aperiam cumque animi. Aut eius impedit saepe blanditiis. Totam molestias magnam minima fugiat.`,
  `Unde a inventore et. Sed esse ut. Atque ducimus quibusdam fuga quas id qui fuga.`,
  `Eaque natus adipisci soluta nostrum dolorem. Nesciunt ipsum molestias ut aliquid natus ut omnis qui fugiat. Dolor et rem. Ut neque voluptatem blanditiis quasi ullam deleniti.`,
  `Nam et error exercitationem qui voluptate optio. Officia omnis qui accusantium ipsam qui. Quia sequi nulla perspiciatis optio vero omnis maxime omnis ipsum. Perspiciatis consequuntur asperiores veniam dolores.`,
  `Perspiciatis nulla ut ut ut voluptates totam consectetur eligendi qui. Optio ut cum. Dolorum sapiente qui laborum. Impedit temporibus totam delectus nihil. Voluptatem corrupti rem.`,
  `Distinctio omnis similique omnis eos. Repellat cumque rerum nisi. Reiciendis soluta non ut veniam temporibus. Accusantium et dolorem voluptas harum. Nemo eius voluptate dicta et hic nemo. Dolorem assumenda et beatae molestias sit quo mollitia quis consequatur.`,
  `Sed ut mollitia tempore ipsam et illum doloribus ut. Occaecati ratione veritatis explicabo. Omnis nam omnis sunt placeat tempore accusantium placeat distinctio velit.`,
  `Eum illo dicta et perspiciatis ut blanditiis eos sequi. Ea veritatis aut et voluptas aut. Laborum eos quia tempore a culpa.`,
  `Aut quos quae dolores repudiandae similique perferendis perferendis earum laudantium. Facere placeat natus nobis. Eius vitae ullam dolorem.`,
  `Vero dolorem et voluptatem fugit tempore a quam iure. Fuga consequatur corrupti sunt asperiores vitae. Libero totam repellendus animi debitis illum et sunt officia.`,
  `Cupiditate illum officiis id molestiae. Numquam non molestiae aliquid et natus sed hic. Alias quia explicabo sed corrupti sint. Natus in et odio qui unde facilis quia. Est sit eius laboriosam aliquid non aperiam quia quo corporis.`,
  `Et a ab. Optio aspernatur minus tempora amet vitae consectetur inventore cumque. Sed et omnis. Aspernatur a magnam.`,
  `Ipsum omnis et. Quia ea et autem tempore consequuntur veniam dolorem officiis. Ipsa dicta et ut quidem quia doloremque. Sequi vitae doloremque temporibus. Deserunt incidunt id aperiam itaque natus. Earum sit eaque quas incidunt nihil.`,
  `Quae consequatur reiciendis. Consequatur non optio. Eaque id placeat. Commodi quo officia aut repudiandae reiciendis tempore voluptatem et. Ut accusamus qui itaque maxime aliquam. Fugit ut animi molestiae porro maiores.`,
  `Modi hic asperiores ab cumque quam est aut. Voluptas atque quos molestias. Ut excepturi distinctio ipsam aspernatur sit.`,
  `Sunt totam facilis. Quam commodi voluptatem veniam. Tempora deleniti itaque fugit nihil voluptas.`,
  `Ipsam aliquam velit nobis repellendus officiis aut deserunt id et. Nihil sunt aut dolores aut. Dolores est ipsa quia et laborum quidem laborum accusamus id. Facilis odit quod hic laudantium saepe omnis nisi in sint. Sed cupiditate possimus id.`,
  `Magnam non eveniet optio optio ut aliquid atque. Velit libero aspernatur quis laborum consequatur laudantium. Tempora facere optio fugit accusantium ut. Omnis aspernatur reprehenderit autem esse ut ut enim voluptatibus.`,
];
