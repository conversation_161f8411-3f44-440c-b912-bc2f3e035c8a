export const PRODUCT_GENDER_OPTIONS = [
  { label: 'Men', value: 'Men' },
  { label: 'Women', value: 'Women' },
  { label: 'Kids', value: 'Kids' },
];

export const PRODUCT_CATEGORY_OPTIONS = ['Shose', 'Apparel', 'Accessories'];

export const PRODUCT_RATING_OPTIONS = [
  'up4Star',
  'up3Star',
  'up2Star',
  'up1Star',
];

export const PRODUCT_COLOR_OPTIONS = [
  '#FF4842',
  '#1890FF',
  '#FFC0CB',
  '#00AB55',
  '#FFC107',
  '#7F00FF',
  '#000000',
  '#FFFFFF',
];

export const PRODUCT_COLOR_NAME_OPTIONS = [
  { value: '#FF4842', label: 'Red' },
  { value: '#1890FF', label: 'Blue' },
  { value: '#FFC0CB', label: 'Pink' },
  { value: '#00AB55', label: 'Green' },
  { value: '#FFC107', label: 'Yellow' },
  { value: '#7F00FF', label: 'Violet' },
  { value: '#000000', label: 'Black' },
  { value: '#FFFFFF', label: 'White' },
];

export const PRODUCT_SIZE_OPTIONS = [
  { value: '7', label: '7' },
  { value: '8', label: '8' },
  { value: '8.5', label: '8.5' },
  { value: '9', label: '9' },
  { value: '9.5', label: '9.5' },
  { value: '10', label: '10' },
  { value: '10.5', label: '10.5' },
  { value: '11', label: '11' },
  { value: '11.5', label: '11.5' },
  { value: '12', label: '12' },
  { value: '13', label: '13' },
];

export const PRODUCT_STOCK_OPTIONS = [
  { value: 'in stock', label: 'In stock' },
  { value: 'low stock', label: 'Low stock' },
  { value: 'out of stock', label: 'Out of stock' },
];

export const PRODUCT_PUBLISH_OPTIONS = [
  { value: 'published', label: 'Published' },
  { value: 'draft', label: 'Draft' },
];

export const PRODUCT_SORT_OPTIONS = [
  { value: 'featured', label: 'Featured' },
  { value: 'newest', label: 'Newest' },
  { value: 'priceDesc', label: 'Price: High - Low' },
  { value: 'priceAsc', label: 'Price: Low - High' },
];

export const PRODUCT_CATEGORY_GROUP_OPTIONS = [
  {
    group: 'Clothing',
    classify: ['Shirts', 'T-shirts', 'Jeans', 'Leather', 'Accessories'],
  },
  {
    group: 'Tailored',
    classify: ['Suits', 'Blazers', 'Trousers', 'Waistcoats', 'Apparel'],
  },
  {
    group: 'Accessories',
    classify: ['Shoes', 'Backpacks and bags', 'Bracelets', 'Face masks'],
  },
];

export const PRODUCT_CHECKOUT_STEPS = ['Cart', 'Billing & address', 'Payment'];

export const _products = [
  {
    id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b1',
    gender: ['Kids'],
    images: [
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-1.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-2.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-3.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-4.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-5.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-6.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-7.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-8.webp',
    ],
    reviews: [
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b1',
        name: 'Jayvion Simon',
        postedAt: '2024-08-19T18:58:39+00:00',
        comment:
          'The sun slowly set over the horizon, painting the sky in vibrant hues of orange and pink.',
        isPurchased: true,
        rating: 4.2,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-1.webp',
        helpful: 9911,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b2',
        name: 'Lucian Obrien',
        postedAt: '2024-08-18T17:58:39+00:00',
        comment:
          'She eagerly opened the gift, her eyes sparkling with excitement.',
        isPurchased: true,
        rating: 3.7,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-2.webp',
        helpful: 1947,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-1.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b3',
        name: 'Deja Brady',
        postedAt: '2024-08-17T16:58:39+00:00',
        comment:
          'The old oak tree stood tall and majestic, its branches swaying gently in the breeze.',
        isPurchased: true,
        rating: 4.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-3.webp',
        helpful: 9124,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b4',
        name: 'Harrison Stein',
        postedAt: '2024-08-16T15:58:39+00:00',
        comment:
          'The aroma of freshly brewed coffee filled the air, awakening my senses.',
        isPurchased: false,
        rating: 3.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-4.webp',
        helpful: 6984,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-3.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-4.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b5',
        name: 'Reece Chung',
        postedAt: '2024-08-15T14:58:39+00:00',
        comment:
          'The children giggled with joy as they ran through the sprinklers on a hot summer day.',
        isPurchased: false,
        rating: 0.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-5.webp',
        helpful: 8488,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b6',
        name: 'Lainey Davidson',
        postedAt: '2024-08-14T13:58:39+00:00',
        comment:
          'He carefully crafted a beautiful sculpture out of clay, his hands skillfully shaping the intricate details.',
        isPurchased: true,
        rating: 3,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-6.webp',
        helpful: 2034,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-6.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-7.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-8.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b7',
        name: 'Cristopher Cardenas',
        postedAt: '2024-08-13T12:58:39+00:00',
        comment:
          'The concert was a mesmerizing experience, with the music filling the venue and the crowd cheering in delight.',
        isPurchased: false,
        rating: 2.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-7.webp',
        helpful: 3364,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b8',
        name: 'Melanie Noble',
        postedAt: '2024-08-12T11:58:39+00:00',
        comment:
          'The waves crashed against the shore, creating a soothing symphony of sound.',
        isPurchased: false,
        rating: 2.8,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-8.webp',
        helpful: 8401,
        attachments: [],
      },
    ],
    publish: 'draft',
    ratings: [
      {
        name: '1 Star',
        starCount: 9911,
        reviewCount: 1947,
      },
      {
        name: '2 Star',
        starCount: 1947,
        reviewCount: 9124,
      },
      {
        name: '3 Star',
        starCount: 9124,
        reviewCount: 6984,
      },
      {
        name: '4 Star',
        starCount: 6984,
        reviewCount: 8488,
      },
      {
        name: '5 Star',
        starCount: 8488,
        reviewCount: 2034,
      },
    ],
    category: 'Accessories',
    available: 0,
    priceSale: 83.74,
    taxes: 10,
    quantity: 80,
    inventoryType: 'out of stock',
    tags: [
      'Technology',
      'Health and Wellness',
      'Travel',
      'Finance',
      'Education',
    ],
    code: '38BEE270',
    description:
      '\n<h6>Specifications</h6>\n<table>\n  <tbody>\n    <tr>\n      <td>Category</td>\n      <td>Mobile</td>\n    </tr>\n    <tr>\n      <td>Manufacturer</td>\n      <td>Apple</td>\n    </tr>\n    <tr>\n      <td>Warranty</td>\n      <td>12 Months</td>\n    </tr>\n    <tr>\n      <td>Serial number</td>\n      <td>358607726380311</td>\n    </tr>\n    <tr>\n      <td>Ships from</td>\n      <td>United States</td>\n    </tr>\n  </tbody>\n</table>\n\n<h6>Product details</h6>\n<ul>\n  <li>\n    <p>The foam sockliner feels soft and comfortable</p>\n  </li>\n  <li>\n    <p>Pull tab</p>\n  </li>\n  <li>\n    <p>Not intended for use as Personal Protective Equipment</p>\n  </li>\n  <li>\n    <p>Colour Shown: White/Black/Oxygen Purple/Action Grape</p>\n  </li>\n  <li>\n    <p>Style: 921826-109</p>\n  </li>\n  <li>\n    <p>Country/Region of Origin: China</p>\n  </li>\n</ul>\n<h6>Benefits</h6>\n<ul>\n  <li>\n    <p>Mesh and synthetic materials on the upper keep the fluid look of the OG while adding comfort</p>\n    and durability.\n  </li>\n  <li>\n    <p>Originally designed for performance running, the full-length Max Air unit adds soft, comfortable cushio</p>\n    ning underfoot.\n  </li>\n  <li>\n    <p>The foam midsole feels springy and soft.</p>\n  </li>\n  <li>\n    <p>The rubber outsole adds traction and durability.</p>\n  </li>\n</ul>\n<h6>Delivery and returns</h6>\n<p>Your order of $200 or more gets free standard delivery.</p>\n<ul>\n  <li>\n    <p>Standard delivered 4-5 Business Days</p>\n  </li>\n  <li>\n    <p>Express delivered 2-4 Business Days</p>\n  </li>\n</ul>\n<p>Orders are processed and delivered Monday-Friday (excluding public holidays)</p>\n\n',
    sku: 'WW75K5210YW/SV',
    createdAt: '2024-08-19T18:58:39+00:00',
    name: 'Urban Explorer Sneakers',
    price: 83.74,
    coverUrl:
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-1.webp',
    colors: ['#FF4842', '#1890FF'],
    totalRatings: 4.2,
    totalSold: 763,
    totalReviews: 1947,
    newLabel: {
      enabled: false,
      content: 'NEW',
    },
    saleLabel: {
      enabled: false,
      content: 'SALE',
    },
    sizes: [
      '6',
      '7',
      '8',
      '8.5',
      '9',
      '9.5',
      '10',
      '10.5',
      '11',
      '11.5',
      '12',
      '13',
    ],
    subDescription:
      'Featuring the original ripple design inspired by Japanese bullet trains, the Nike Air Max 97 lets you push your style full-speed ahead.',
  },
  {
    id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b2',
    gender: ['Men'],
    images: [
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-1.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-2.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-3.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-4.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-5.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-6.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-7.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-8.webp',
    ],
    reviews: [
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b1',
        name: 'Jayvion Simon',
        postedAt: '2024-08-19T18:58:39+00:00',
        comment:
          'The sun slowly set over the horizon, painting the sky in vibrant hues of orange and pink.',
        isPurchased: true,
        rating: 4.2,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-1.webp',
        helpful: 9911,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b2',
        name: 'Lucian Obrien',
        postedAt: '2024-08-18T17:58:39+00:00',
        comment:
          'She eagerly opened the gift, her eyes sparkling with excitement.',
        isPurchased: true,
        rating: 3.7,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-2.webp',
        helpful: 1947,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-1.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b3',
        name: 'Deja Brady',
        postedAt: '2024-08-17T16:58:39+00:00',
        comment:
          'The old oak tree stood tall and majestic, its branches swaying gently in the breeze.',
        isPurchased: true,
        rating: 4.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-3.webp',
        helpful: 9124,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b4',
        name: 'Harrison Stein',
        postedAt: '2024-08-16T15:58:39+00:00',
        comment:
          'The aroma of freshly brewed coffee filled the air, awakening my senses.',
        isPurchased: false,
        rating: 3.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-4.webp',
        helpful: 6984,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-3.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-4.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b5',
        name: 'Reece Chung',
        postedAt: '2024-08-15T14:58:39+00:00',
        comment:
          'The children giggled with joy as they ran through the sprinklers on a hot summer day.',
        isPurchased: false,
        rating: 0.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-5.webp',
        helpful: 8488,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b6',
        name: 'Lainey Davidson',
        postedAt: '2024-08-14T13:58:39+00:00',
        comment:
          'He carefully crafted a beautiful sculpture out of clay, his hands skillfully shaping the intricate details.',
        isPurchased: true,
        rating: 3,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-6.webp',
        helpful: 2034,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-6.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-7.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-8.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b7',
        name: 'Cristopher Cardenas',
        postedAt: '2024-08-13T12:58:39+00:00',
        comment:
          'The concert was a mesmerizing experience, with the music filling the venue and the crowd cheering in delight.',
        isPurchased: false,
        rating: 2.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-7.webp',
        helpful: 3364,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b8',
        name: 'Melanie Noble',
        postedAt: '2024-08-12T11:58:39+00:00',
        comment:
          'The waves crashed against the shore, creating a soothing symphony of sound.',
        isPurchased: false,
        rating: 2.8,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-8.webp',
        helpful: 8401,
        attachments: [],
      },
    ],
    publish: 'published',
    ratings: [
      {
        name: '1 Star',
        starCount: 9911,
        reviewCount: 1947,
      },
      {
        name: '2 Star',
        starCount: 1947,
        reviewCount: 9124,
      },
      {
        name: '3 Star',
        starCount: 9124,
        reviewCount: 6984,
      },
      {
        name: '4 Star',
        starCount: 6984,
        reviewCount: 8488,
      },
      {
        name: '5 Star',
        starCount: 8488,
        reviewCount: 2034,
      },
    ],
    category: 'Shose',
    available: 72,
    priceSale: null,
    taxes: 10,
    quantity: 80,
    inventoryType: 'in stock',
    tags: [
      'Technology',
      'Health and Wellness',
      'Travel',
      'Finance',
      'Education',
    ],
    code: '38BEE271',
    description:
      '\n<h6>Specifications</h6>\n<table>\n  <tbody>\n    <tr>\n      <td>Category</td>\n      <td>Mobile</td>\n    </tr>\n    <tr>\n      <td>Manufacturer</td>\n      <td>Apple</td>\n    </tr>\n    <tr>\n      <td>Warranty</td>\n      <td>12 Months</td>\n    </tr>\n    <tr>\n      <td>Serial number</td>\n      <td>358607726380311</td>\n    </tr>\n    <tr>\n      <td>Ships from</td>\n      <td>United States</td>\n    </tr>\n  </tbody>\n</table>\n\n<h6>Product details</h6>\n<ul>\n  <li>\n    <p>The foam sockliner feels soft and comfortable</p>\n  </li>\n  <li>\n    <p>Pull tab</p>\n  </li>\n  <li>\n    <p>Not intended for use as Personal Protective Equipment</p>\n  </li>\n  <li>\n    <p>Colour Shown: White/Black/Oxygen Purple/Action Grape</p>\n  </li>\n  <li>\n    <p>Style: 921826-109</p>\n  </li>\n  <li>\n    <p>Country/Region of Origin: China</p>\n  </li>\n</ul>\n<h6>Benefits</h6>\n<ul>\n  <li>\n    <p>Mesh and synthetic materials on the upper keep the fluid look of the OG while adding comfort</p>\n    and durability.\n  </li>\n  <li>\n    <p>Originally designed for performance running, the full-length Max Air unit adds soft, comfortable cushio</p>\n    ning underfoot.\n  </li>\n  <li>\n    <p>The foam midsole feels springy and soft.</p>\n  </li>\n  <li>\n    <p>The rubber outsole adds traction and durability.</p>\n  </li>\n</ul>\n<h6>Delivery and returns</h6>\n<p>Your order of $200 or more gets free standard delivery.</p>\n<ul>\n  <li>\n    <p>Standard delivered 4-5 Business Days</p>\n  </li>\n  <li>\n    <p>Express delivered 2-4 Business Days</p>\n  </li>\n</ul>\n<p>Orders are processed and delivered Monday-Friday (excluding public holidays)</p>\n\n',
    sku: 'WW75K5211YW/SV',
    createdAt: '2024-08-18T17:58:39+00:00',
    name: 'Classic Leather Loafers',
    price: 97.14,
    coverUrl:
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-2.webp',
    colors: ['#1890FF', '#FFC0CB'],
    totalRatings: 3.7,
    totalSold: 684,
    totalReviews: 9124,
    newLabel: {
      enabled: true,
      content: 'NEW',
    },
    saleLabel: {
      enabled: false,
      content: 'SALE',
    },
    sizes: [
      '6',
      '7',
      '8',
      '8.5',
      '9',
      '9.5',
      '10',
      '10.5',
      '11',
      '11.5',
      '12',
      '13',
    ],
    subDescription:
      'Featuring the original ripple design inspired by Japanese bullet trains, the Nike Air Max 97 lets you push your style full-speed ahead.',
  },
  {
    id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b3',
    gender: ['Women', 'Kids'],
    images: [
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-1.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-2.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-3.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-4.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-5.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-6.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-7.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-8.webp',
    ],
    reviews: [
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b1',
        name: 'Jayvion Simon',
        postedAt: '2024-08-19T18:58:39+00:00',
        comment:
          'The sun slowly set over the horizon, painting the sky in vibrant hues of orange and pink.',
        isPurchased: true,
        rating: 4.2,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-1.webp',
        helpful: 9911,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b2',
        name: 'Lucian Obrien',
        postedAt: '2024-08-18T17:58:39+00:00',
        comment:
          'She eagerly opened the gift, her eyes sparkling with excitement.',
        isPurchased: true,
        rating: 3.7,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-2.webp',
        helpful: 1947,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-1.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b3',
        name: 'Deja Brady',
        postedAt: '2024-08-17T16:58:39+00:00',
        comment:
          'The old oak tree stood tall and majestic, its branches swaying gently in the breeze.',
        isPurchased: true,
        rating: 4.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-3.webp',
        helpful: 9124,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b4',
        name: 'Harrison Stein',
        postedAt: '2024-08-16T15:58:39+00:00',
        comment:
          'The aroma of freshly brewed coffee filled the air, awakening my senses.',
        isPurchased: false,
        rating: 3.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-4.webp',
        helpful: 6984,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-3.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-4.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b5',
        name: 'Reece Chung',
        postedAt: '2024-08-15T14:58:39+00:00',
        comment:
          'The children giggled with joy as they ran through the sprinklers on a hot summer day.',
        isPurchased: false,
        rating: 0.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-5.webp',
        helpful: 8488,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b6',
        name: 'Lainey Davidson',
        postedAt: '2024-08-14T13:58:39+00:00',
        comment:
          'He carefully crafted a beautiful sculpture out of clay, his hands skillfully shaping the intricate details.',
        isPurchased: true,
        rating: 3,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-6.webp',
        helpful: 2034,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-6.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-7.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-8.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b7',
        name: 'Cristopher Cardenas',
        postedAt: '2024-08-13T12:58:39+00:00',
        comment:
          'The concert was a mesmerizing experience, with the music filling the venue and the crowd cheering in delight.',
        isPurchased: false,
        rating: 2.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-7.webp',
        helpful: 3364,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b8',
        name: 'Melanie Noble',
        postedAt: '2024-08-12T11:58:39+00:00',
        comment:
          'The waves crashed against the shore, creating a soothing symphony of sound.',
        isPurchased: false,
        rating: 2.8,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-8.webp',
        helpful: 8401,
        attachments: [],
      },
    ],
    publish: 'published',
    ratings: [
      {
        name: '1 Star',
        starCount: 9911,
        reviewCount: 1947,
      },
      {
        name: '2 Star',
        starCount: 1947,
        reviewCount: 9124,
      },
      {
        name: '3 Star',
        starCount: 9124,
        reviewCount: 6984,
      },
      {
        name: '4 Star',
        starCount: 6984,
        reviewCount: 8488,
      },
      {
        name: '5 Star',
        starCount: 8488,
        reviewCount: 2034,
      },
    ],
    category: 'Apparel',
    available: 10,
    priceSale: null,
    taxes: 10,
    quantity: 80,
    inventoryType: 'low stock',
    tags: [
      'Technology',
      'Health and Wellness',
      'Travel',
      'Finance',
      'Education',
    ],
    code: '38BEE272',
    description:
      '\n<h6>Specifications</h6>\n<table>\n  <tbody>\n    <tr>\n      <td>Category</td>\n      <td>Mobile</td>\n    </tr>\n    <tr>\n      <td>Manufacturer</td>\n      <td>Apple</td>\n    </tr>\n    <tr>\n      <td>Warranty</td>\n      <td>12 Months</td>\n    </tr>\n    <tr>\n      <td>Serial number</td>\n      <td>358607726380311</td>\n    </tr>\n    <tr>\n      <td>Ships from</td>\n      <td>United States</td>\n    </tr>\n  </tbody>\n</table>\n\n<h6>Product details</h6>\n<ul>\n  <li>\n    <p>The foam sockliner feels soft and comfortable</p>\n  </li>\n  <li>\n    <p>Pull tab</p>\n  </li>\n  <li>\n    <p>Not intended for use as Personal Protective Equipment</p>\n  </li>\n  <li>\n    <p>Colour Shown: White/Black/Oxygen Purple/Action Grape</p>\n  </li>\n  <li>\n    <p>Style: 921826-109</p>\n  </li>\n  <li>\n    <p>Country/Region of Origin: China</p>\n  </li>\n</ul>\n<h6>Benefits</h6>\n<ul>\n  <li>\n    <p>Mesh and synthetic materials on the upper keep the fluid look of the OG while adding comfort</p>\n    and durability.\n  </li>\n  <li>\n    <p>Originally designed for performance running, the full-length Max Air unit adds soft, comfortable cushio</p>\n    ning underfoot.\n  </li>\n  <li>\n    <p>The foam midsole feels springy and soft.</p>\n  </li>\n  <li>\n    <p>The rubber outsole adds traction and durability.</p>\n  </li>\n</ul>\n<h6>Delivery and returns</h6>\n<p>Your order of $200 or more gets free standard delivery.</p>\n<ul>\n  <li>\n    <p>Standard delivered 4-5 Business Days</p>\n  </li>\n  <li>\n    <p>Express delivered 2-4 Business Days</p>\n  </li>\n</ul>\n<p>Orders are processed and delivered Monday-Friday (excluding public holidays)</p>\n\n',
    sku: 'WW75K5212YW/SV',
    createdAt: '2024-08-17T16:58:39+00:00',
    name: 'Mountain Trekking Boots',
    price: 68.71,
    coverUrl:
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-3.webp',
    colors: ['#FFC0CB', '#00AB55'],
    totalRatings: 4.5,
    totalSold: 451,
    totalReviews: 6984,
    newLabel: {
      enabled: true,
      content: 'NEW',
    },
    saleLabel: {
      enabled: false,
      content: 'SALE',
    },
    sizes: [
      '6',
      '7',
      '8',
      '8.5',
      '9',
      '9.5',
      '10',
      '10.5',
      '11',
      '11.5',
      '12',
      '13',
    ],
    subDescription:
      'Featuring the original ripple design inspired by Japanese bullet trains, the Nike Air Max 97 lets you push your style full-speed ahead.',
  },
  {
    id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b4',
    gender: ['Men'],
    images: [
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-1.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-2.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-3.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-4.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-5.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-6.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-7.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-8.webp',
    ],
    reviews: [
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b1',
        name: 'Jayvion Simon',
        postedAt: '2024-08-19T18:58:39+00:00',
        comment:
          'The sun slowly set over the horizon, painting the sky in vibrant hues of orange and pink.',
        isPurchased: true,
        rating: 4.2,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-1.webp',
        helpful: 9911,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b2',
        name: 'Lucian Obrien',
        postedAt: '2024-08-18T17:58:39+00:00',
        comment:
          'She eagerly opened the gift, her eyes sparkling with excitement.',
        isPurchased: true,
        rating: 3.7,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-2.webp',
        helpful: 1947,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-1.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b3',
        name: 'Deja Brady',
        postedAt: '2024-08-17T16:58:39+00:00',
        comment:
          'The old oak tree stood tall and majestic, its branches swaying gently in the breeze.',
        isPurchased: true,
        rating: 4.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-3.webp',
        helpful: 9124,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b4',
        name: 'Harrison Stein',
        postedAt: '2024-08-16T15:58:39+00:00',
        comment:
          'The aroma of freshly brewed coffee filled the air, awakening my senses.',
        isPurchased: false,
        rating: 3.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-4.webp',
        helpful: 6984,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-3.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-4.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b5',
        name: 'Reece Chung',
        postedAt: '2024-08-15T14:58:39+00:00',
        comment:
          'The children giggled with joy as they ran through the sprinklers on a hot summer day.',
        isPurchased: false,
        rating: 0.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-5.webp',
        helpful: 8488,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b6',
        name: 'Lainey Davidson',
        postedAt: '2024-08-14T13:58:39+00:00',
        comment:
          'He carefully crafted a beautiful sculpture out of clay, his hands skillfully shaping the intricate details.',
        isPurchased: true,
        rating: 3,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-6.webp',
        helpful: 2034,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-6.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-7.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-8.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b7',
        name: 'Cristopher Cardenas',
        postedAt: '2024-08-13T12:58:39+00:00',
        comment:
          'The concert was a mesmerizing experience, with the music filling the venue and the crowd cheering in delight.',
        isPurchased: false,
        rating: 2.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-7.webp',
        helpful: 3364,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b8',
        name: 'Melanie Noble',
        postedAt: '2024-08-12T11:58:39+00:00',
        comment:
          'The waves crashed against the shore, creating a soothing symphony of sound.',
        isPurchased: false,
        rating: 2.8,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-8.webp',
        helpful: 8401,
        attachments: [],
      },
    ],
    publish: 'draft',
    ratings: [
      {
        name: '1 Star',
        starCount: 9911,
        reviewCount: 1947,
      },
      {
        name: '2 Star',
        starCount: 1947,
        reviewCount: 9124,
      },
      {
        name: '3 Star',
        starCount: 9124,
        reviewCount: 6984,
      },
      {
        name: '4 Star',
        starCount: 6984,
        reviewCount: 8488,
      },
      {
        name: '5 Star',
        starCount: 8488,
        reviewCount: 2034,
      },
    ],
    category: 'Shose',
    available: 72,
    priceSale: 85.21,
    taxes: 10,
    quantity: 80,
    inventoryType: 'in stock',
    tags: [
      'Technology',
      'Health and Wellness',
      'Travel',
      'Finance',
      'Education',
    ],
    code: '38BEE273',
    description:
      '\n<h6>Specifications</h6>\n<table>\n  <tbody>\n    <tr>\n      <td>Category</td>\n      <td>Mobile</td>\n    </tr>\n    <tr>\n      <td>Manufacturer</td>\n      <td>Apple</td>\n    </tr>\n    <tr>\n      <td>Warranty</td>\n      <td>12 Months</td>\n    </tr>\n    <tr>\n      <td>Serial number</td>\n      <td>358607726380311</td>\n    </tr>\n    <tr>\n      <td>Ships from</td>\n      <td>United States</td>\n    </tr>\n  </tbody>\n</table>\n\n<h6>Product details</h6>\n<ul>\n  <li>\n    <p>The foam sockliner feels soft and comfortable</p>\n  </li>\n  <li>\n    <p>Pull tab</p>\n  </li>\n  <li>\n    <p>Not intended for use as Personal Protective Equipment</p>\n  </li>\n  <li>\n    <p>Colour Shown: White/Black/Oxygen Purple/Action Grape</p>\n  </li>\n  <li>\n    <p>Style: 921826-109</p>\n  </li>\n  <li>\n    <p>Country/Region of Origin: China</p>\n  </li>\n</ul>\n<h6>Benefits</h6>\n<ul>\n  <li>\n    <p>Mesh and synthetic materials on the upper keep the fluid look of the OG while adding comfort</p>\n    and durability.\n  </li>\n  <li>\n    <p>Originally designed for performance running, the full-length Max Air unit adds soft, comfortable cushio</p>\n    ning underfoot.\n  </li>\n  <li>\n    <p>The foam midsole feels springy and soft.</p>\n  </li>\n  <li>\n    <p>The rubber outsole adds traction and durability.</p>\n  </li>\n</ul>\n<h6>Delivery and returns</h6>\n<p>Your order of $200 or more gets free standard delivery.</p>\n<ul>\n  <li>\n    <p>Standard delivered 4-5 Business Days</p>\n  </li>\n  <li>\n    <p>Express delivered 2-4 Business Days</p>\n  </li>\n</ul>\n<p>Orders are processed and delivered Monday-Friday (excluding public holidays)</p>\n\n',
    sku: 'WW75K5213YW/SV',
    createdAt: '2024-08-16T15:58:39+00:00',
    name: 'Elegance Stiletto Heels',
    price: 85.21,
    coverUrl:
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-4.webp',
    colors: ['#00AB55', '#FFC107', '#7F00FF'],
    totalRatings: 3.5,
    totalSold: 433,
    totalReviews: 8488,
    newLabel: {
      enabled: true,
      content: 'NEW',
    },
    saleLabel: {
      enabled: false,
      content: 'SALE',
    },
    sizes: [
      '6',
      '7',
      '8',
      '8.5',
      '9',
      '9.5',
      '10',
      '10.5',
      '11',
      '11.5',
      '12',
      '13',
    ],
    subDescription:
      'Featuring the original ripple design inspired by Japanese bullet trains, the Nike Air Max 97 lets you push your style full-speed ahead.',
  },
  {
    id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b5',
    gender: ['Women', 'Kids'],
    images: [
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-1.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-2.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-3.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-4.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-5.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-6.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-7.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-8.webp',
    ],
    reviews: [
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b1',
        name: 'Jayvion Simon',
        postedAt: '2024-08-19T18:58:39+00:00',
        comment:
          'The sun slowly set over the horizon, painting the sky in vibrant hues of orange and pink.',
        isPurchased: true,
        rating: 4.2,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-1.webp',
        helpful: 9911,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b2',
        name: 'Lucian Obrien',
        postedAt: '2024-08-18T17:58:39+00:00',
        comment:
          'She eagerly opened the gift, her eyes sparkling with excitement.',
        isPurchased: true,
        rating: 3.7,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-2.webp',
        helpful: 1947,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-1.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b3',
        name: 'Deja Brady',
        postedAt: '2024-08-17T16:58:39+00:00',
        comment:
          'The old oak tree stood tall and majestic, its branches swaying gently in the breeze.',
        isPurchased: true,
        rating: 4.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-3.webp',
        helpful: 9124,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b4',
        name: 'Harrison Stein',
        postedAt: '2024-08-16T15:58:39+00:00',
        comment:
          'The aroma of freshly brewed coffee filled the air, awakening my senses.',
        isPurchased: false,
        rating: 3.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-4.webp',
        helpful: 6984,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-3.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-4.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b5',
        name: 'Reece Chung',
        postedAt: '2024-08-15T14:58:39+00:00',
        comment:
          'The children giggled with joy as they ran through the sprinklers on a hot summer day.',
        isPurchased: false,
        rating: 0.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-5.webp',
        helpful: 8488,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b6',
        name: 'Lainey Davidson',
        postedAt: '2024-08-14T13:58:39+00:00',
        comment:
          'He carefully crafted a beautiful sculpture out of clay, his hands skillfully shaping the intricate details.',
        isPurchased: true,
        rating: 3,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-6.webp',
        helpful: 2034,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-6.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-7.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-8.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b7',
        name: 'Cristopher Cardenas',
        postedAt: '2024-08-13T12:58:39+00:00',
        comment:
          'The concert was a mesmerizing experience, with the music filling the venue and the crowd cheering in delight.',
        isPurchased: false,
        rating: 2.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-7.webp',
        helpful: 3364,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b8',
        name: 'Melanie Noble',
        postedAt: '2024-08-12T11:58:39+00:00',
        comment:
          'The waves crashed against the shore, creating a soothing symphony of sound.',
        isPurchased: false,
        rating: 2.8,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-8.webp',
        helpful: 8401,
        attachments: [],
      },
    ],
    publish: 'published',
    ratings: [
      {
        name: '1 Star',
        starCount: 9911,
        reviewCount: 1947,
      },
      {
        name: '2 Star',
        starCount: 1947,
        reviewCount: 9124,
      },
      {
        name: '3 Star',
        starCount: 9124,
        reviewCount: 6984,
      },
      {
        name: '4 Star',
        starCount: 6984,
        reviewCount: 8488,
      },
      {
        name: '5 Star',
        starCount: 8488,
        reviewCount: 2034,
      },
    ],
    category: 'Apparel',
    available: 10,
    priceSale: null,
    taxes: 10,
    quantity: 80,
    inventoryType: 'low stock',
    tags: [
      'Technology',
      'Health and Wellness',
      'Travel',
      'Finance',
      'Education',
    ],
    code: '38BEE274',
    description:
      '\n<h6>Specifications</h6>\n<table>\n  <tbody>\n    <tr>\n      <td>Category</td>\n      <td>Mobile</td>\n    </tr>\n    <tr>\n      <td>Manufacturer</td>\n      <td>Apple</td>\n    </tr>\n    <tr>\n      <td>Warranty</td>\n      <td>12 Months</td>\n    </tr>\n    <tr>\n      <td>Serial number</td>\n      <td>358607726380311</td>\n    </tr>\n    <tr>\n      <td>Ships from</td>\n      <td>United States</td>\n    </tr>\n  </tbody>\n</table>\n\n<h6>Product details</h6>\n<ul>\n  <li>\n    <p>The foam sockliner feels soft and comfortable</p>\n  </li>\n  <li>\n    <p>Pull tab</p>\n  </li>\n  <li>\n    <p>Not intended for use as Personal Protective Equipment</p>\n  </li>\n  <li>\n    <p>Colour Shown: White/Black/Oxygen Purple/Action Grape</p>\n  </li>\n  <li>\n    <p>Style: 921826-109</p>\n  </li>\n  <li>\n    <p>Country/Region of Origin: China</p>\n  </li>\n</ul>\n<h6>Benefits</h6>\n<ul>\n  <li>\n    <p>Mesh and synthetic materials on the upper keep the fluid look of the OG while adding comfort</p>\n    and durability.\n  </li>\n  <li>\n    <p>Originally designed for performance running, the full-length Max Air unit adds soft, comfortable cushio</p>\n    ning underfoot.\n  </li>\n  <li>\n    <p>The foam midsole feels springy and soft.</p>\n  </li>\n  <li>\n    <p>The rubber outsole adds traction and durability.</p>\n  </li>\n</ul>\n<h6>Delivery and returns</h6>\n<p>Your order of $200 or more gets free standard delivery.</p>\n<ul>\n  <li>\n    <p>Standard delivered 4-5 Business Days</p>\n  </li>\n  <li>\n    <p>Express delivered 2-4 Business Days</p>\n  </li>\n</ul>\n<p>Orders are processed and delivered Monday-Friday (excluding public holidays)</p>\n\n',
    sku: 'WW75K5214YW/SV',
    createdAt: '2024-08-15T14:58:39+00:00',
    name: 'Comfy Running Shoes',
    price: 52.17,
    coverUrl:
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-5.webp',
    colors: ['#FFC107', '#7F00FF'],
    totalRatings: 0.5,
    totalSold: 463,
    totalReviews: 2034,
    newLabel: {
      enabled: false,
      content: 'NEW',
    },
    saleLabel: {
      enabled: true,
      content: 'SALE',
    },
    sizes: [
      '6',
      '7',
      '8',
      '8.5',
      '9',
      '9.5',
      '10',
      '10.5',
      '11',
      '11.5',
      '12',
      '13',
    ],
    subDescription:
      'Featuring the original ripple design inspired by Japanese bullet trains, the Nike Air Max 97 lets you push your style full-speed ahead.',
  },
  {
    id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b6',
    gender: ['Men'],
    images: [
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-1.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-2.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-3.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-4.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-5.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-6.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-7.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-8.webp',
    ],
    reviews: [
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b1',
        name: 'Jayvion Simon',
        postedAt: '2024-08-19T18:58:39+00:00',
        comment:
          'The sun slowly set over the horizon, painting the sky in vibrant hues of orange and pink.',
        isPurchased: true,
        rating: 4.2,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-1.webp',
        helpful: 9911,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b2',
        name: 'Lucian Obrien',
        postedAt: '2024-08-18T17:58:39+00:00',
        comment:
          'She eagerly opened the gift, her eyes sparkling with excitement.',
        isPurchased: true,
        rating: 3.7,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-2.webp',
        helpful: 1947,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-1.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b3',
        name: 'Deja Brady',
        postedAt: '2024-08-17T16:58:39+00:00',
        comment:
          'The old oak tree stood tall and majestic, its branches swaying gently in the breeze.',
        isPurchased: true,
        rating: 4.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-3.webp',
        helpful: 9124,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b4',
        name: 'Harrison Stein',
        postedAt: '2024-08-16T15:58:39+00:00',
        comment:
          'The aroma of freshly brewed coffee filled the air, awakening my senses.',
        isPurchased: false,
        rating: 3.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-4.webp',
        helpful: 6984,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-3.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-4.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b5',
        name: 'Reece Chung',
        postedAt: '2024-08-15T14:58:39+00:00',
        comment:
          'The children giggled with joy as they ran through the sprinklers on a hot summer day.',
        isPurchased: false,
        rating: 0.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-5.webp',
        helpful: 8488,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b6',
        name: 'Lainey Davidson',
        postedAt: '2024-08-14T13:58:39+00:00',
        comment:
          'He carefully crafted a beautiful sculpture out of clay, his hands skillfully shaping the intricate details.',
        isPurchased: true,
        rating: 3,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-6.webp',
        helpful: 2034,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-6.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-7.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-8.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b7',
        name: 'Cristopher Cardenas',
        postedAt: '2024-08-13T12:58:39+00:00',
        comment:
          'The concert was a mesmerizing experience, with the music filling the venue and the crowd cheering in delight.',
        isPurchased: false,
        rating: 2.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-7.webp',
        helpful: 3364,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b8',
        name: 'Melanie Noble',
        postedAt: '2024-08-12T11:58:39+00:00',
        comment:
          'The waves crashed against the shore, creating a soothing symphony of sound.',
        isPurchased: false,
        rating: 2.8,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-8.webp',
        helpful: 8401,
        attachments: [],
      },
    ],
    publish: 'published',
    ratings: [
      {
        name: '1 Star',
        starCount: 9911,
        reviewCount: 1947,
      },
      {
        name: '2 Star',
        starCount: 1947,
        reviewCount: 9124,
      },
      {
        name: '3 Star',
        starCount: 9124,
        reviewCount: 6984,
      },
      {
        name: '4 Star',
        starCount: 6984,
        reviewCount: 8488,
      },
      {
        name: '5 Star',
        starCount: 8488,
        reviewCount: 2034,
      },
    ],
    category: 'Shose',
    available: 72,
    priceSale: null,
    taxes: 10,
    quantity: 80,
    inventoryType: 'in stock',
    tags: [
      'Technology',
      'Health and Wellness',
      'Travel',
      'Finance',
      'Education',
    ],
    code: '38BEE275',
    description:
      '\n<h6>Specifications</h6>\n<table>\n  <tbody>\n    <tr>\n      <td>Category</td>\n      <td>Mobile</td>\n    </tr>\n    <tr>\n      <td>Manufacturer</td>\n      <td>Apple</td>\n    </tr>\n    <tr>\n      <td>Warranty</td>\n      <td>12 Months</td>\n    </tr>\n    <tr>\n      <td>Serial number</td>\n      <td>358607726380311</td>\n    </tr>\n    <tr>\n      <td>Ships from</td>\n      <td>United States</td>\n    </tr>\n  </tbody>\n</table>\n\n<h6>Product details</h6>\n<ul>\n  <li>\n    <p>The foam sockliner feels soft and comfortable</p>\n  </li>\n  <li>\n    <p>Pull tab</p>\n  </li>\n  <li>\n    <p>Not intended for use as Personal Protective Equipment</p>\n  </li>\n  <li>\n    <p>Colour Shown: White/Black/Oxygen Purple/Action Grape</p>\n  </li>\n  <li>\n    <p>Style: 921826-109</p>\n  </li>\n  <li>\n    <p>Country/Region of Origin: China</p>\n  </li>\n</ul>\n<h6>Benefits</h6>\n<ul>\n  <li>\n    <p>Mesh and synthetic materials on the upper keep the fluid look of the OG while adding comfort</p>\n    and durability.\n  </li>\n  <li>\n    <p>Originally designed for performance running, the full-length Max Air unit adds soft, comfortable cushio</p>\n    ning underfoot.\n  </li>\n  <li>\n    <p>The foam midsole feels springy and soft.</p>\n  </li>\n  <li>\n    <p>The rubber outsole adds traction and durability.</p>\n  </li>\n</ul>\n<h6>Delivery and returns</h6>\n<p>Your order of $200 or more gets free standard delivery.</p>\n<ul>\n  <li>\n    <p>Standard delivered 4-5 Business Days</p>\n  </li>\n  <li>\n    <p>Express delivered 2-4 Business Days</p>\n  </li>\n</ul>\n<p>Orders are processed and delivered Monday-Friday (excluding public holidays)</p>\n\n',
    sku: 'WW75K5215YW/SV',
    createdAt: '2024-08-14T13:58:39+00:00',
    name: 'Chic Ballet Flats',
    price: 25.18,
    coverUrl:
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-6.webp',
    colors: ['#7F00FF'],
    totalRatings: 3,
    totalSold: 951,
    totalReviews: 3364,
    newLabel: {
      enabled: false,
      content: 'NEW',
    },
    saleLabel: {
      enabled: true,
      content: 'SALE',
    },
    sizes: [
      '6',
      '7',
      '8',
      '8.5',
      '9',
      '9.5',
      '10',
      '10.5',
      '11',
      '11.5',
      '12',
      '13',
    ],
    subDescription:
      'Featuring the original ripple design inspired by Japanese bullet trains, the Nike Air Max 97 lets you push your style full-speed ahead.',
  },
  {
    id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b7',
    gender: ['Kids'],
    images: [
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-1.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-2.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-3.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-4.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-5.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-6.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-7.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-8.webp',
    ],
    reviews: [
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b1',
        name: 'Jayvion Simon',
        postedAt: '2024-08-19T18:58:39+00:00',
        comment:
          'The sun slowly set over the horizon, painting the sky in vibrant hues of orange and pink.',
        isPurchased: true,
        rating: 4.2,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-1.webp',
        helpful: 9911,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b2',
        name: 'Lucian Obrien',
        postedAt: '2024-08-18T17:58:39+00:00',
        comment:
          'She eagerly opened the gift, her eyes sparkling with excitement.',
        isPurchased: true,
        rating: 3.7,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-2.webp',
        helpful: 1947,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-1.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b3',
        name: 'Deja Brady',
        postedAt: '2024-08-17T16:58:39+00:00',
        comment:
          'The old oak tree stood tall and majestic, its branches swaying gently in the breeze.',
        isPurchased: true,
        rating: 4.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-3.webp',
        helpful: 9124,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b4',
        name: 'Harrison Stein',
        postedAt: '2024-08-16T15:58:39+00:00',
        comment:
          'The aroma of freshly brewed coffee filled the air, awakening my senses.',
        isPurchased: false,
        rating: 3.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-4.webp',
        helpful: 6984,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-3.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-4.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b5',
        name: 'Reece Chung',
        postedAt: '2024-08-15T14:58:39+00:00',
        comment:
          'The children giggled with joy as they ran through the sprinklers on a hot summer day.',
        isPurchased: false,
        rating: 0.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-5.webp',
        helpful: 8488,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b6',
        name: 'Lainey Davidson',
        postedAt: '2024-08-14T13:58:39+00:00',
        comment:
          'He carefully crafted a beautiful sculpture out of clay, his hands skillfully shaping the intricate details.',
        isPurchased: true,
        rating: 3,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-6.webp',
        helpful: 2034,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-6.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-7.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-8.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b7',
        name: 'Cristopher Cardenas',
        postedAt: '2024-08-13T12:58:39+00:00',
        comment:
          'The concert was a mesmerizing experience, with the music filling the venue and the crowd cheering in delight.',
        isPurchased: false,
        rating: 2.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-7.webp',
        helpful: 3364,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b8',
        name: 'Melanie Noble',
        postedAt: '2024-08-12T11:58:39+00:00',
        comment:
          'The waves crashed against the shore, creating a soothing symphony of sound.',
        isPurchased: false,
        rating: 2.8,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-8.webp',
        helpful: 8401,
        attachments: [],
      },
    ],
    publish: 'draft',
    ratings: [
      {
        name: '1 Star',
        starCount: 9911,
        reviewCount: 1947,
      },
      {
        name: '2 Star',
        starCount: 1947,
        reviewCount: 9124,
      },
      {
        name: '3 Star',
        starCount: 9124,
        reviewCount: 6984,
      },
      {
        name: '4 Star',
        starCount: 6984,
        reviewCount: 8488,
      },
      {
        name: '5 Star',
        starCount: 8488,
        reviewCount: 2034,
      },
    ],
    category: 'Accessories',
    available: 0,
    priceSale: 43.84,
    taxes: 10,
    quantity: 80,
    inventoryType: 'out of stock',
    tags: [
      'Technology',
      'Health and Wellness',
      'Travel',
      'Finance',
      'Education',
    ],
    code: '38BEE276',
    description:
      '\n<h6>Specifications</h6>\n<table>\n  <tbody>\n    <tr>\n      <td>Category</td>\n      <td>Mobile</td>\n    </tr>\n    <tr>\n      <td>Manufacturer</td>\n      <td>Apple</td>\n    </tr>\n    <tr>\n      <td>Warranty</td>\n      <td>12 Months</td>\n    </tr>\n    <tr>\n      <td>Serial number</td>\n      <td>358607726380311</td>\n    </tr>\n    <tr>\n      <td>Ships from</td>\n      <td>United States</td>\n    </tr>\n  </tbody>\n</table>\n\n<h6>Product details</h6>\n<ul>\n  <li>\n    <p>The foam sockliner feels soft and comfortable</p>\n  </li>\n  <li>\n    <p>Pull tab</p>\n  </li>\n  <li>\n    <p>Not intended for use as Personal Protective Equipment</p>\n  </li>\n  <li>\n    <p>Colour Shown: White/Black/Oxygen Purple/Action Grape</p>\n  </li>\n  <li>\n    <p>Style: 921826-109</p>\n  </li>\n  <li>\n    <p>Country/Region of Origin: China</p>\n  </li>\n</ul>\n<h6>Benefits</h6>\n<ul>\n  <li>\n    <p>Mesh and synthetic materials on the upper keep the fluid look of the OG while adding comfort</p>\n    and durability.\n  </li>\n  <li>\n    <p>Originally designed for performance running, the full-length Max Air unit adds soft, comfortable cushio</p>\n    ning underfoot.\n  </li>\n  <li>\n    <p>The foam midsole feels springy and soft.</p>\n  </li>\n  <li>\n    <p>The rubber outsole adds traction and durability.</p>\n  </li>\n</ul>\n<h6>Delivery and returns</h6>\n<p>Your order of $200 or more gets free standard delivery.</p>\n<ul>\n  <li>\n    <p>Standard delivered 4-5 Business Days</p>\n  </li>\n  <li>\n    <p>Express delivered 2-4 Business Days</p>\n  </li>\n</ul>\n<p>Orders are processed and delivered Monday-Friday (excluding public holidays)</p>\n\n',
    sku: 'WW75K5216YW/SV',
    createdAt: '2024-08-13T12:58:39+00:00',
    name: 'Vintage Oxford Shoes',
    price: 43.84,
    coverUrl:
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-7.webp',
    colors: ['#FF4842', '#1890FF'],
    totalRatings: 2.5,
    totalSold: 194,
    totalReviews: 8401,
    newLabel: {
      enabled: false,
      content: 'NEW',
    },
    saleLabel: {
      enabled: false,
      content: 'SALE',
    },
    sizes: [
      '6',
      '7',
      '8',
      '8.5',
      '9',
      '9.5',
      '10',
      '10.5',
      '11',
      '11.5',
      '12',
      '13',
    ],
    subDescription:
      'Featuring the original ripple design inspired by Japanese bullet trains, the Nike Air Max 97 lets you push your style full-speed ahead.',
  },
  {
    id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b8',
    gender: ['Men'],
    images: [
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-1.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-2.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-3.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-4.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-5.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-6.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-7.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-8.webp',
    ],
    reviews: [
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b1',
        name: 'Jayvion Simon',
        postedAt: '2024-08-19T18:58:39+00:00',
        comment:
          'The sun slowly set over the horizon, painting the sky in vibrant hues of orange and pink.',
        isPurchased: true,
        rating: 4.2,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-1.webp',
        helpful: 9911,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b2',
        name: 'Lucian Obrien',
        postedAt: '2024-08-18T17:58:39+00:00',
        comment:
          'She eagerly opened the gift, her eyes sparkling with excitement.',
        isPurchased: true,
        rating: 3.7,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-2.webp',
        helpful: 1947,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-1.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b3',
        name: 'Deja Brady',
        postedAt: '2024-08-17T16:58:39+00:00',
        comment:
          'The old oak tree stood tall and majestic, its branches swaying gently in the breeze.',
        isPurchased: true,
        rating: 4.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-3.webp',
        helpful: 9124,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b4',
        name: 'Harrison Stein',
        postedAt: '2024-08-16T15:58:39+00:00',
        comment:
          'The aroma of freshly brewed coffee filled the air, awakening my senses.',
        isPurchased: false,
        rating: 3.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-4.webp',
        helpful: 6984,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-3.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-4.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b5',
        name: 'Reece Chung',
        postedAt: '2024-08-15T14:58:39+00:00',
        comment:
          'The children giggled with joy as they ran through the sprinklers on a hot summer day.',
        isPurchased: false,
        rating: 0.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-5.webp',
        helpful: 8488,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b6',
        name: 'Lainey Davidson',
        postedAt: '2024-08-14T13:58:39+00:00',
        comment:
          'He carefully crafted a beautiful sculpture out of clay, his hands skillfully shaping the intricate details.',
        isPurchased: true,
        rating: 3,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-6.webp',
        helpful: 2034,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-6.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-7.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-8.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b7',
        name: 'Cristopher Cardenas',
        postedAt: '2024-08-13T12:58:39+00:00',
        comment:
          'The concert was a mesmerizing experience, with the music filling the venue and the crowd cheering in delight.',
        isPurchased: false,
        rating: 2.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-7.webp',
        helpful: 3364,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b8',
        name: 'Melanie Noble',
        postedAt: '2024-08-12T11:58:39+00:00',
        comment:
          'The waves crashed against the shore, creating a soothing symphony of sound.',
        isPurchased: false,
        rating: 2.8,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-8.webp',
        helpful: 8401,
        attachments: [],
      },
    ],
    publish: 'published',
    ratings: [
      {
        name: '1 Star',
        starCount: 9911,
        reviewCount: 1947,
      },
      {
        name: '2 Star',
        starCount: 1947,
        reviewCount: 9124,
      },
      {
        name: '3 Star',
        starCount: 9124,
        reviewCount: 6984,
      },
      {
        name: '4 Star',
        starCount: 6984,
        reviewCount: 8488,
      },
      {
        name: '5 Star',
        starCount: 8488,
        reviewCount: 2034,
      },
    ],
    category: 'Shose',
    available: 72,
    priceSale: null,
    taxes: 10,
    quantity: 80,
    inventoryType: 'in stock',
    tags: [
      'Technology',
      'Health and Wellness',
      'Travel',
      'Finance',
      'Education',
    ],
    code: '38BEE277',
    description:
      '\n<h6>Specifications</h6>\n<table>\n  <tbody>\n    <tr>\n      <td>Category</td>\n      <td>Mobile</td>\n    </tr>\n    <tr>\n      <td>Manufacturer</td>\n      <td>Apple</td>\n    </tr>\n    <tr>\n      <td>Warranty</td>\n      <td>12 Months</td>\n    </tr>\n    <tr>\n      <td>Serial number</td>\n      <td>358607726380311</td>\n    </tr>\n    <tr>\n      <td>Ships from</td>\n      <td>United States</td>\n    </tr>\n  </tbody>\n</table>\n\n<h6>Product details</h6>\n<ul>\n  <li>\n    <p>The foam sockliner feels soft and comfortable</p>\n  </li>\n  <li>\n    <p>Pull tab</p>\n  </li>\n  <li>\n    <p>Not intended for use as Personal Protective Equipment</p>\n  </li>\n  <li>\n    <p>Colour Shown: White/Black/Oxygen Purple/Action Grape</p>\n  </li>\n  <li>\n    <p>Style: 921826-109</p>\n  </li>\n  <li>\n    <p>Country/Region of Origin: China</p>\n  </li>\n</ul>\n<h6>Benefits</h6>\n<ul>\n  <li>\n    <p>Mesh and synthetic materials on the upper keep the fluid look of the OG while adding comfort</p>\n    and durability.\n  </li>\n  <li>\n    <p>Originally designed for performance running, the full-length Max Air unit adds soft, comfortable cushio</p>\n    ning underfoot.\n  </li>\n  <li>\n    <p>The foam midsole feels springy and soft.</p>\n  </li>\n  <li>\n    <p>The rubber outsole adds traction and durability.</p>\n  </li>\n</ul>\n<h6>Delivery and returns</h6>\n<p>Your order of $200 or more gets free standard delivery.</p>\n<ul>\n  <li>\n    <p>Standard delivered 4-5 Business Days</p>\n  </li>\n  <li>\n    <p>Express delivered 2-4 Business Days</p>\n  </li>\n</ul>\n<p>Orders are processed and delivered Monday-Friday (excluding public holidays)</p>\n\n',
    sku: 'WW75K5217YW/SV',
    createdAt: '2024-08-12T11:58:39+00:00',
    name: 'Waterproof Hiking Boots',
    price: 60.98,
    coverUrl:
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-8.webp',
    colors: ['#FFC107', '#7F00FF'],
    totalRatings: 2.8,
    totalSold: 425,
    totalReviews: 8996,
    newLabel: {
      enabled: false,
      content: 'NEW',
    },
    saleLabel: {
      enabled: false,
      content: 'SALE',
    },
    sizes: [
      '6',
      '7',
      '8',
      '8.5',
      '9',
      '9.5',
      '10',
      '10.5',
      '11',
      '11.5',
      '12',
      '13',
    ],
    subDescription:
      'Featuring the original ripple design inspired by Japanese bullet trains, the Nike Air Max 97 lets you push your style full-speed ahead.',
  },
  {
    id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b9',
    gender: ['Women', 'Kids'],
    images: [
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-1.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-2.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-3.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-4.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-5.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-6.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-7.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-8.webp',
    ],
    reviews: [
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b1',
        name: 'Jayvion Simon',
        postedAt: '2024-08-19T18:58:39+00:00',
        comment:
          'The sun slowly set over the horizon, painting the sky in vibrant hues of orange and pink.',
        isPurchased: true,
        rating: 4.2,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-1.webp',
        helpful: 9911,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b2',
        name: 'Lucian Obrien',
        postedAt: '2024-08-18T17:58:39+00:00',
        comment:
          'She eagerly opened the gift, her eyes sparkling with excitement.',
        isPurchased: true,
        rating: 3.7,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-2.webp',
        helpful: 1947,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-1.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b3',
        name: 'Deja Brady',
        postedAt: '2024-08-17T16:58:39+00:00',
        comment:
          'The old oak tree stood tall and majestic, its branches swaying gently in the breeze.',
        isPurchased: true,
        rating: 4.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-3.webp',
        helpful: 9124,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b4',
        name: 'Harrison Stein',
        postedAt: '2024-08-16T15:58:39+00:00',
        comment:
          'The aroma of freshly brewed coffee filled the air, awakening my senses.',
        isPurchased: false,
        rating: 3.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-4.webp',
        helpful: 6984,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-3.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-4.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b5',
        name: 'Reece Chung',
        postedAt: '2024-08-15T14:58:39+00:00',
        comment:
          'The children giggled with joy as they ran through the sprinklers on a hot summer day.',
        isPurchased: false,
        rating: 0.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-5.webp',
        helpful: 8488,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b6',
        name: 'Lainey Davidson',
        postedAt: '2024-08-14T13:58:39+00:00',
        comment:
          'He carefully crafted a beautiful sculpture out of clay, his hands skillfully shaping the intricate details.',
        isPurchased: true,
        rating: 3,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-6.webp',
        helpful: 2034,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-6.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-7.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-8.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b7',
        name: 'Cristopher Cardenas',
        postedAt: '2024-08-13T12:58:39+00:00',
        comment:
          'The concert was a mesmerizing experience, with the music filling the venue and the crowd cheering in delight.',
        isPurchased: false,
        rating: 2.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-7.webp',
        helpful: 3364,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b8',
        name: 'Melanie Noble',
        postedAt: '2024-08-12T11:58:39+00:00',
        comment:
          'The waves crashed against the shore, creating a soothing symphony of sound.',
        isPurchased: false,
        rating: 2.8,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-8.webp',
        helpful: 8401,
        attachments: [],
      },
    ],
    publish: 'published',
    ratings: [
      {
        name: '1 Star',
        starCount: 9911,
        reviewCount: 1947,
      },
      {
        name: '2 Star',
        starCount: 1947,
        reviewCount: 9124,
      },
      {
        name: '3 Star',
        starCount: 9124,
        reviewCount: 6984,
      },
      {
        name: '4 Star',
        starCount: 6984,
        reviewCount: 8488,
      },
      {
        name: '5 Star',
        starCount: 8488,
        reviewCount: 2034,
      },
    ],
    category: 'Apparel',
    available: 10,
    priceSale: null,
    taxes: 10,
    quantity: 80,
    inventoryType: 'low stock',
    tags: [
      'Technology',
      'Health and Wellness',
      'Travel',
      'Finance',
      'Education',
    ],
    code: '38BEE278',
    description:
      '\n<h6>Specifications</h6>\n<table>\n  <tbody>\n    <tr>\n      <td>Category</td>\n      <td>Mobile</td>\n    </tr>\n    <tr>\n      <td>Manufacturer</td>\n      <td>Apple</td>\n    </tr>\n    <tr>\n      <td>Warranty</td>\n      <td>12 Months</td>\n    </tr>\n    <tr>\n      <td>Serial number</td>\n      <td>358607726380311</td>\n    </tr>\n    <tr>\n      <td>Ships from</td>\n      <td>United States</td>\n    </tr>\n  </tbody>\n</table>\n\n<h6>Product details</h6>\n<ul>\n  <li>\n    <p>The foam sockliner feels soft and comfortable</p>\n  </li>\n  <li>\n    <p>Pull tab</p>\n  </li>\n  <li>\n    <p>Not intended for use as Personal Protective Equipment</p>\n  </li>\n  <li>\n    <p>Colour Shown: White/Black/Oxygen Purple/Action Grape</p>\n  </li>\n  <li>\n    <p>Style: 921826-109</p>\n  </li>\n  <li>\n    <p>Country/Region of Origin: China</p>\n  </li>\n</ul>\n<h6>Benefits</h6>\n<ul>\n  <li>\n    <p>Mesh and synthetic materials on the upper keep the fluid look of the OG while adding comfort</p>\n    and durability.\n  </li>\n  <li>\n    <p>Originally designed for performance running, the full-length Max Air unit adds soft, comfortable cushio</p>\n    ning underfoot.\n  </li>\n  <li>\n    <p>The foam midsole feels springy and soft.</p>\n  </li>\n  <li>\n    <p>The rubber outsole adds traction and durability.</p>\n  </li>\n</ul>\n<h6>Delivery and returns</h6>\n<p>Your order of $200 or more gets free standard delivery.</p>\n<ul>\n  <li>\n    <p>Standard delivered 4-5 Business Days</p>\n  </li>\n  <li>\n    <p>Express delivered 2-4 Business Days</p>\n  </li>\n</ul>\n<p>Orders are processed and delivered Monday-Friday (excluding public holidays)</p>\n\n',
    sku: 'WW75K5218YW/SV',
    createdAt: '2024-08-11T10:58:39+00:00',
    name: 'Casual Slip-On Sneakers',
    price: 98.42,
    coverUrl:
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-9.webp',
    colors: ['#FFC0CB', '#00AB55'],
    totalRatings: 4.9,
    totalSold: 435,
    totalReviews: 5271,
    newLabel: {
      enabled: false,
      content: 'NEW',
    },
    saleLabel: {
      enabled: false,
      content: 'SALE',
    },
    sizes: [
      '6',
      '7',
      '8',
      '8.5',
      '9',
      '9.5',
      '10',
      '10.5',
      '11',
      '11.5',
      '12',
      '13',
    ],
    subDescription:
      'Featuring the original ripple design inspired by Japanese bullet trains, the Nike Air Max 97 lets you push your style full-speed ahead.',
  },
  {
    id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b10',
    gender: ['Men'],
    images: [
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-1.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-2.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-3.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-4.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-5.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-6.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-7.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-8.webp',
    ],
    reviews: [
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b1',
        name: 'Jayvion Simon',
        postedAt: '2024-08-19T18:58:39+00:00',
        comment:
          'The sun slowly set over the horizon, painting the sky in vibrant hues of orange and pink.',
        isPurchased: true,
        rating: 4.2,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-1.webp',
        helpful: 9911,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b2',
        name: 'Lucian Obrien',
        postedAt: '2024-08-18T17:58:39+00:00',
        comment:
          'She eagerly opened the gift, her eyes sparkling with excitement.',
        isPurchased: true,
        rating: 3.7,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-2.webp',
        helpful: 1947,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-1.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b3',
        name: 'Deja Brady',
        postedAt: '2024-08-17T16:58:39+00:00',
        comment:
          'The old oak tree stood tall and majestic, its branches swaying gently in the breeze.',
        isPurchased: true,
        rating: 4.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-3.webp',
        helpful: 9124,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b4',
        name: 'Harrison Stein',
        postedAt: '2024-08-16T15:58:39+00:00',
        comment:
          'The aroma of freshly brewed coffee filled the air, awakening my senses.',
        isPurchased: false,
        rating: 3.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-4.webp',
        helpful: 6984,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-3.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-4.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b5',
        name: 'Reece Chung',
        postedAt: '2024-08-15T14:58:39+00:00',
        comment:
          'The children giggled with joy as they ran through the sprinklers on a hot summer day.',
        isPurchased: false,
        rating: 0.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-5.webp',
        helpful: 8488,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b6',
        name: 'Lainey Davidson',
        postedAt: '2024-08-14T13:58:39+00:00',
        comment:
          'He carefully crafted a beautiful sculpture out of clay, his hands skillfully shaping the intricate details.',
        isPurchased: true,
        rating: 3,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-6.webp',
        helpful: 2034,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-6.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-7.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-8.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b7',
        name: 'Cristopher Cardenas',
        postedAt: '2024-08-13T12:58:39+00:00',
        comment:
          'The concert was a mesmerizing experience, with the music filling the venue and the crowd cheering in delight.',
        isPurchased: false,
        rating: 2.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-7.webp',
        helpful: 3364,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b8',
        name: 'Melanie Noble',
        postedAt: '2024-08-12T11:58:39+00:00',
        comment:
          'The waves crashed against the shore, creating a soothing symphony of sound.',
        isPurchased: false,
        rating: 2.8,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-8.webp',
        helpful: 8401,
        attachments: [],
      },
    ],
    publish: 'draft',
    ratings: [
      {
        name: '1 Star',
        starCount: 9911,
        reviewCount: 1947,
      },
      {
        name: '2 Star',
        starCount: 1947,
        reviewCount: 9124,
      },
      {
        name: '3 Star',
        starCount: 9124,
        reviewCount: 6984,
      },
      {
        name: '4 Star',
        starCount: 6984,
        reviewCount: 8488,
      },
      {
        name: '5 Star',
        starCount: 8488,
        reviewCount: 2034,
      },
    ],
    category: 'Shose',
    available: 72,
    priceSale: 53.37,
    taxes: 10,
    quantity: 80,
    inventoryType: 'in stock',
    tags: [
      'Technology',
      'Health and Wellness',
      'Travel',
      'Finance',
      'Education',
    ],
    code: '38BEE279',
    description:
      '\n<h6>Specifications</h6>\n<table>\n  <tbody>\n    <tr>\n      <td>Category</td>\n      <td>Mobile</td>\n    </tr>\n    <tr>\n      <td>Manufacturer</td>\n      <td>Apple</td>\n    </tr>\n    <tr>\n      <td>Warranty</td>\n      <td>12 Months</td>\n    </tr>\n    <tr>\n      <td>Serial number</td>\n      <td>358607726380311</td>\n    </tr>\n    <tr>\n      <td>Ships from</td>\n      <td>United States</td>\n    </tr>\n  </tbody>\n</table>\n\n<h6>Product details</h6>\n<ul>\n  <li>\n    <p>The foam sockliner feels soft and comfortable</p>\n  </li>\n  <li>\n    <p>Pull tab</p>\n  </li>\n  <li>\n    <p>Not intended for use as Personal Protective Equipment</p>\n  </li>\n  <li>\n    <p>Colour Shown: White/Black/Oxygen Purple/Action Grape</p>\n  </li>\n  <li>\n    <p>Style: 921826-109</p>\n  </li>\n  <li>\n    <p>Country/Region of Origin: China</p>\n  </li>\n</ul>\n<h6>Benefits</h6>\n<ul>\n  <li>\n    <p>Mesh and synthetic materials on the upper keep the fluid look of the OG while adding comfort</p>\n    and durability.\n  </li>\n  <li>\n    <p>Originally designed for performance running, the full-length Max Air unit adds soft, comfortable cushio</p>\n    ning underfoot.\n  </li>\n  <li>\n    <p>The foam midsole feels springy and soft.</p>\n  </li>\n  <li>\n    <p>The rubber outsole adds traction and durability.</p>\n  </li>\n</ul>\n<h6>Delivery and returns</h6>\n<p>Your order of $200 or more gets free standard delivery.</p>\n<ul>\n  <li>\n    <p>Standard delivered 4-5 Business Days</p>\n  </li>\n  <li>\n    <p>Express delivered 2-4 Business Days</p>\n  </li>\n</ul>\n<p>Orders are processed and delivered Monday-Friday (excluding public holidays)</p>\n\n',
    sku: 'WW75K5219YW/SV',
    createdAt: '2024-08-10T09:58:39+00:00',
    name: 'Premium Dress Shoes',
    price: 53.37,
    coverUrl:
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-10.webp',
    colors: ['#FFC0CB', '#00AB55', '#FFC107', '#7F00FF'],
    totalRatings: 3.6,
    totalSold: 807,
    totalReviews: 8478,
    newLabel: {
      enabled: false,
      content: 'NEW',
    },
    saleLabel: {
      enabled: false,
      content: 'SALE',
    },
    sizes: [
      '6',
      '7',
      '8',
      '8.5',
      '9',
      '9.5',
      '10',
      '10.5',
      '11',
      '11.5',
      '12',
      '13',
    ],
    subDescription:
      'Featuring the original ripple design inspired by Japanese bullet trains, the Nike Air Max 97 lets you push your style full-speed ahead.',
  },
  {
    id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b11',
    gender: ['Women', 'Kids'],
    images: [
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-1.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-2.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-3.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-4.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-5.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-6.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-7.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-8.webp',
    ],
    reviews: [
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b1',
        name: 'Jayvion Simon',
        postedAt: '2024-08-19T18:58:39+00:00',
        comment:
          'The sun slowly set over the horizon, painting the sky in vibrant hues of orange and pink.',
        isPurchased: true,
        rating: 4.2,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-1.webp',
        helpful: 9911,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b2',
        name: 'Lucian Obrien',
        postedAt: '2024-08-18T17:58:39+00:00',
        comment:
          'She eagerly opened the gift, her eyes sparkling with excitement.',
        isPurchased: true,
        rating: 3.7,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-2.webp',
        helpful: 1947,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-1.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b3',
        name: 'Deja Brady',
        postedAt: '2024-08-17T16:58:39+00:00',
        comment:
          'The old oak tree stood tall and majestic, its branches swaying gently in the breeze.',
        isPurchased: true,
        rating: 4.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-3.webp',
        helpful: 9124,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b4',
        name: 'Harrison Stein',
        postedAt: '2024-08-16T15:58:39+00:00',
        comment:
          'The aroma of freshly brewed coffee filled the air, awakening my senses.',
        isPurchased: false,
        rating: 3.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-4.webp',
        helpful: 6984,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-3.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-4.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b5',
        name: 'Reece Chung',
        postedAt: '2024-08-15T14:58:39+00:00',
        comment:
          'The children giggled with joy as they ran through the sprinklers on a hot summer day.',
        isPurchased: false,
        rating: 0.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-5.webp',
        helpful: 8488,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b6',
        name: 'Lainey Davidson',
        postedAt: '2024-08-14T13:58:39+00:00',
        comment:
          'He carefully crafted a beautiful sculpture out of clay, his hands skillfully shaping the intricate details.',
        isPurchased: true,
        rating: 3,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-6.webp',
        helpful: 2034,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-6.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-7.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-8.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b7',
        name: 'Cristopher Cardenas',
        postedAt: '2024-08-13T12:58:39+00:00',
        comment:
          'The concert was a mesmerizing experience, with the music filling the venue and the crowd cheering in delight.',
        isPurchased: false,
        rating: 2.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-7.webp',
        helpful: 3364,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b8',
        name: 'Melanie Noble',
        postedAt: '2024-08-12T11:58:39+00:00',
        comment:
          'The waves crashed against the shore, creating a soothing symphony of sound.',
        isPurchased: false,
        rating: 2.8,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-8.webp',
        helpful: 8401,
        attachments: [],
      },
    ],
    publish: 'published',
    ratings: [
      {
        name: '1 Star',
        starCount: 9911,
        reviewCount: 1947,
      },
      {
        name: '2 Star',
        starCount: 1947,
        reviewCount: 9124,
      },
      {
        name: '3 Star',
        starCount: 9124,
        reviewCount: 6984,
      },
      {
        name: '4 Star',
        starCount: 6984,
        reviewCount: 8488,
      },
      {
        name: '5 Star',
        starCount: 8488,
        reviewCount: 2034,
      },
    ],
    category: 'Apparel',
    available: 10,
    priceSale: null,
    taxes: 10,
    quantity: 80,
    inventoryType: 'low stock',
    tags: [
      'Technology',
      'Health and Wellness',
      'Travel',
      'Finance',
      'Education',
    ],
    code: '38BEE2710',
    description:
      '\n<h6>Specifications</h6>\n<table>\n  <tbody>\n    <tr>\n      <td>Category</td>\n      <td>Mobile</td>\n    </tr>\n    <tr>\n      <td>Manufacturer</td>\n      <td>Apple</td>\n    </tr>\n    <tr>\n      <td>Warranty</td>\n      <td>12 Months</td>\n    </tr>\n    <tr>\n      <td>Serial number</td>\n      <td>358607726380311</td>\n    </tr>\n    <tr>\n      <td>Ships from</td>\n      <td>United States</td>\n    </tr>\n  </tbody>\n</table>\n\n<h6>Product details</h6>\n<ul>\n  <li>\n    <p>The foam sockliner feels soft and comfortable</p>\n  </li>\n  <li>\n    <p>Pull tab</p>\n  </li>\n  <li>\n    <p>Not intended for use as Personal Protective Equipment</p>\n  </li>\n  <li>\n    <p>Colour Shown: White/Black/Oxygen Purple/Action Grape</p>\n  </li>\n  <li>\n    <p>Style: 921826-109</p>\n  </li>\n  <li>\n    <p>Country/Region of Origin: China</p>\n  </li>\n</ul>\n<h6>Benefits</h6>\n<ul>\n  <li>\n    <p>Mesh and synthetic materials on the upper keep the fluid look of the OG while adding comfort</p>\n    and durability.\n  </li>\n  <li>\n    <p>Originally designed for performance running, the full-length Max Air unit adds soft, comfortable cushio</p>\n    ning underfoot.\n  </li>\n  <li>\n    <p>The foam midsole feels springy and soft.</p>\n  </li>\n  <li>\n    <p>The rubber outsole adds traction and durability.</p>\n  </li>\n</ul>\n<h6>Delivery and returns</h6>\n<p>Your order of $200 or more gets free standard delivery.</p>\n<ul>\n  <li>\n    <p>Standard delivered 4-5 Business Days</p>\n  </li>\n  <li>\n    <p>Express delivered 2-4 Business Days</p>\n  </li>\n</ul>\n<p>Orders are processed and delivered Monday-Friday (excluding public holidays)</p>\n\n',
    sku: 'WW75K52110YW/SV',
    createdAt: '2024-08-09T08:58:39+00:00',
    name: 'Sporty Trail Runners',
    price: 72.75,
    coverUrl:
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-11.webp',
    colors: ['#00AB55', '#FFC107', '#7F00FF'],
    totalRatings: 2.5,
    totalSold: 521,
    totalReviews: 1139,
    newLabel: {
      enabled: false,
      content: 'NEW',
    },
    saleLabel: {
      enabled: false,
      content: 'SALE',
    },
    sizes: [
      '6',
      '7',
      '8',
      '8.5',
      '9',
      '9.5',
      '10',
      '10.5',
      '11',
      '11.5',
      '12',
      '13',
    ],
    subDescription:
      'Featuring the original ripple design inspired by Japanese bullet trains, the Nike Air Max 97 lets you push your style full-speed ahead.',
  },
  {
    id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b12',
    gender: ['Men'],
    images: [
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-1.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-2.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-3.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-4.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-5.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-6.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-7.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-8.webp',
    ],
    reviews: [
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b1',
        name: 'Jayvion Simon',
        postedAt: '2024-08-19T18:58:39+00:00',
        comment:
          'The sun slowly set over the horizon, painting the sky in vibrant hues of orange and pink.',
        isPurchased: true,
        rating: 4.2,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-1.webp',
        helpful: 9911,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b2',
        name: 'Lucian Obrien',
        postedAt: '2024-08-18T17:58:39+00:00',
        comment:
          'She eagerly opened the gift, her eyes sparkling with excitement.',
        isPurchased: true,
        rating: 3.7,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-2.webp',
        helpful: 1947,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-1.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b3',
        name: 'Deja Brady',
        postedAt: '2024-08-17T16:58:39+00:00',
        comment:
          'The old oak tree stood tall and majestic, its branches swaying gently in the breeze.',
        isPurchased: true,
        rating: 4.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-3.webp',
        helpful: 9124,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b4',
        name: 'Harrison Stein',
        postedAt: '2024-08-16T15:58:39+00:00',
        comment:
          'The aroma of freshly brewed coffee filled the air, awakening my senses.',
        isPurchased: false,
        rating: 3.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-4.webp',
        helpful: 6984,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-3.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-4.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b5',
        name: 'Reece Chung',
        postedAt: '2024-08-15T14:58:39+00:00',
        comment:
          'The children giggled with joy as they ran through the sprinklers on a hot summer day.',
        isPurchased: false,
        rating: 0.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-5.webp',
        helpful: 8488,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b6',
        name: 'Lainey Davidson',
        postedAt: '2024-08-14T13:58:39+00:00',
        comment:
          'He carefully crafted a beautiful sculpture out of clay, his hands skillfully shaping the intricate details.',
        isPurchased: true,
        rating: 3,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-6.webp',
        helpful: 2034,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-6.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-7.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-8.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b7',
        name: 'Cristopher Cardenas',
        postedAt: '2024-08-13T12:58:39+00:00',
        comment:
          'The concert was a mesmerizing experience, with the music filling the venue and the crowd cheering in delight.',
        isPurchased: false,
        rating: 2.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-7.webp',
        helpful: 3364,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b8',
        name: 'Melanie Noble',
        postedAt: '2024-08-12T11:58:39+00:00',
        comment:
          'The waves crashed against the shore, creating a soothing symphony of sound.',
        isPurchased: false,
        rating: 2.8,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-8.webp',
        helpful: 8401,
        attachments: [],
      },
    ],
    publish: 'published',
    ratings: [
      {
        name: '1 Star',
        starCount: 9911,
        reviewCount: 1947,
      },
      {
        name: '2 Star',
        starCount: 1947,
        reviewCount: 9124,
      },
      {
        name: '3 Star',
        starCount: 9124,
        reviewCount: 6984,
      },
      {
        name: '4 Star',
        starCount: 6984,
        reviewCount: 8488,
      },
      {
        name: '5 Star',
        starCount: 8488,
        reviewCount: 2034,
      },
    ],
    category: 'Shose',
    available: 72,
    priceSale: null,
    taxes: 10,
    quantity: 80,
    inventoryType: 'in stock',
    tags: [
      'Technology',
      'Health and Wellness',
      'Travel',
      'Finance',
      'Education',
    ],
    code: '38BEE2711',
    description:
      '\n<h6>Specifications</h6>\n<table>\n  <tbody>\n    <tr>\n      <td>Category</td>\n      <td>Mobile</td>\n    </tr>\n    <tr>\n      <td>Manufacturer</td>\n      <td>Apple</td>\n    </tr>\n    <tr>\n      <td>Warranty</td>\n      <td>12 Months</td>\n    </tr>\n    <tr>\n      <td>Serial number</td>\n      <td>358607726380311</td>\n    </tr>\n    <tr>\n      <td>Ships from</td>\n      <td>United States</td>\n    </tr>\n  </tbody>\n</table>\n\n<h6>Product details</h6>\n<ul>\n  <li>\n    <p>The foam sockliner feels soft and comfortable</p>\n  </li>\n  <li>\n    <p>Pull tab</p>\n  </li>\n  <li>\n    <p>Not intended for use as Personal Protective Equipment</p>\n  </li>\n  <li>\n    <p>Colour Shown: White/Black/Oxygen Purple/Action Grape</p>\n  </li>\n  <li>\n    <p>Style: 921826-109</p>\n  </li>\n  <li>\n    <p>Country/Region of Origin: China</p>\n  </li>\n</ul>\n<h6>Benefits</h6>\n<ul>\n  <li>\n    <p>Mesh and synthetic materials on the upper keep the fluid look of the OG while adding comfort</p>\n    and durability.\n  </li>\n  <li>\n    <p>Originally designed for performance running, the full-length Max Air unit adds soft, comfortable cushio</p>\n    ning underfoot.\n  </li>\n  <li>\n    <p>The foam midsole feels springy and soft.</p>\n  </li>\n  <li>\n    <p>The rubber outsole adds traction and durability.</p>\n  </li>\n</ul>\n<h6>Delivery and returns</h6>\n<p>Your order of $200 or more gets free standard delivery.</p>\n<ul>\n  <li>\n    <p>Standard delivered 4-5 Business Days</p>\n  </li>\n  <li>\n    <p>Express delivered 2-4 Business Days</p>\n  </li>\n</ul>\n<p>Orders are processed and delivered Monday-Friday (excluding public holidays)</p>\n\n',
    sku: 'WW75K52111YW/SV',
    createdAt: '2024-08-08T07:58:39+00:00',
    name: 'Sophisticated Brogues',
    price: 56.61,
    coverUrl:
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-12.webp',
    colors: ['#FFC0CB', '#00AB55', '#FFC107', '#7F00FF'],
    totalRatings: 1.7,
    totalSold: 538,
    totalReviews: 8061,
    newLabel: {
      enabled: false,
      content: 'NEW',
    },
    saleLabel: {
      enabled: false,
      content: 'SALE',
    },
    sizes: [
      '6',
      '7',
      '8',
      '8.5',
      '9',
      '9.5',
      '10',
      '10.5',
      '11',
      '11.5',
      '12',
      '13',
    ],
    subDescription:
      'Featuring the original ripple design inspired by Japanese bullet trains, the Nike Air Max 97 lets you push your style full-speed ahead.',
  },
  {
    id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b13',
    gender: ['Kids'],
    images: [
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-1.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-2.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-3.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-4.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-5.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-6.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-7.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-8.webp',
    ],
    reviews: [
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b1',
        name: 'Jayvion Simon',
        postedAt: '2024-08-19T18:58:39+00:00',
        comment:
          'The sun slowly set over the horizon, painting the sky in vibrant hues of orange and pink.',
        isPurchased: true,
        rating: 4.2,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-1.webp',
        helpful: 9911,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b2',
        name: 'Lucian Obrien',
        postedAt: '2024-08-18T17:58:39+00:00',
        comment:
          'She eagerly opened the gift, her eyes sparkling with excitement.',
        isPurchased: true,
        rating: 3.7,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-2.webp',
        helpful: 1947,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-1.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b3',
        name: 'Deja Brady',
        postedAt: '2024-08-17T16:58:39+00:00',
        comment:
          'The old oak tree stood tall and majestic, its branches swaying gently in the breeze.',
        isPurchased: true,
        rating: 4.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-3.webp',
        helpful: 9124,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b4',
        name: 'Harrison Stein',
        postedAt: '2024-08-16T15:58:39+00:00',
        comment:
          'The aroma of freshly brewed coffee filled the air, awakening my senses.',
        isPurchased: false,
        rating: 3.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-4.webp',
        helpful: 6984,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-3.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-4.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b5',
        name: 'Reece Chung',
        postedAt: '2024-08-15T14:58:39+00:00',
        comment:
          'The children giggled with joy as they ran through the sprinklers on a hot summer day.',
        isPurchased: false,
        rating: 0.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-5.webp',
        helpful: 8488,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b6',
        name: 'Lainey Davidson',
        postedAt: '2024-08-14T13:58:39+00:00',
        comment:
          'He carefully crafted a beautiful sculpture out of clay, his hands skillfully shaping the intricate details.',
        isPurchased: true,
        rating: 3,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-6.webp',
        helpful: 2034,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-6.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-7.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-8.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b7',
        name: 'Cristopher Cardenas',
        postedAt: '2024-08-13T12:58:39+00:00',
        comment:
          'The concert was a mesmerizing experience, with the music filling the venue and the crowd cheering in delight.',
        isPurchased: false,
        rating: 2.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-7.webp',
        helpful: 3364,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b8',
        name: 'Melanie Noble',
        postedAt: '2024-08-12T11:58:39+00:00',
        comment:
          'The waves crashed against the shore, creating a soothing symphony of sound.',
        isPurchased: false,
        rating: 2.8,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-8.webp',
        helpful: 8401,
        attachments: [],
      },
    ],
    publish: 'draft',
    ratings: [
      {
        name: '1 Star',
        starCount: 9911,
        reviewCount: 1947,
      },
      {
        name: '2 Star',
        starCount: 1947,
        reviewCount: 9124,
      },
      {
        name: '3 Star',
        starCount: 9124,
        reviewCount: 6984,
      },
      {
        name: '4 Star',
        starCount: 6984,
        reviewCount: 8488,
      },
      {
        name: '5 Star',
        starCount: 8488,
        reviewCount: 2034,
      },
    ],
    category: 'Accessories',
    available: 0,
    priceSale: 64.55,
    taxes: 10,
    quantity: 80,
    inventoryType: 'out of stock',
    tags: [
      'Technology',
      'Health and Wellness',
      'Travel',
      'Finance',
      'Education',
    ],
    code: '38BEE2712',
    description:
      '\n<h6>Specifications</h6>\n<table>\n  <tbody>\n    <tr>\n      <td>Category</td>\n      <td>Mobile</td>\n    </tr>\n    <tr>\n      <td>Manufacturer</td>\n      <td>Apple</td>\n    </tr>\n    <tr>\n      <td>Warranty</td>\n      <td>12 Months</td>\n    </tr>\n    <tr>\n      <td>Serial number</td>\n      <td>358607726380311</td>\n    </tr>\n    <tr>\n      <td>Ships from</td>\n      <td>United States</td>\n    </tr>\n  </tbody>\n</table>\n\n<h6>Product details</h6>\n<ul>\n  <li>\n    <p>The foam sockliner feels soft and comfortable</p>\n  </li>\n  <li>\n    <p>Pull tab</p>\n  </li>\n  <li>\n    <p>Not intended for use as Personal Protective Equipment</p>\n  </li>\n  <li>\n    <p>Colour Shown: White/Black/Oxygen Purple/Action Grape</p>\n  </li>\n  <li>\n    <p>Style: 921826-109</p>\n  </li>\n  <li>\n    <p>Country/Region of Origin: China</p>\n  </li>\n</ul>\n<h6>Benefits</h6>\n<ul>\n  <li>\n    <p>Mesh and synthetic materials on the upper keep the fluid look of the OG while adding comfort</p>\n    and durability.\n  </li>\n  <li>\n    <p>Originally designed for performance running, the full-length Max Air unit adds soft, comfortable cushio</p>\n    ning underfoot.\n  </li>\n  <li>\n    <p>The foam midsole feels springy and soft.</p>\n  </li>\n  <li>\n    <p>The rubber outsole adds traction and durability.</p>\n  </li>\n</ul>\n<h6>Delivery and returns</h6>\n<p>Your order of $200 or more gets free standard delivery.</p>\n<ul>\n  <li>\n    <p>Standard delivered 4-5 Business Days</p>\n  </li>\n  <li>\n    <p>Express delivered 2-4 Business Days</p>\n  </li>\n</ul>\n<p>Orders are processed and delivered Monday-Friday (excluding public holidays)</p>\n\n',
    sku: 'WW75K52112YW/SV',
    createdAt: '2024-08-07T06:58:39+00:00',
    name: 'Beach Sandals',
    price: 64.55,
    coverUrl:
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-13.webp',
    colors: ['#FFC0CB', '#00AB55', '#FFC107', '#7F00FF', '#000000'],
    totalRatings: 3.9,
    totalSold: 839,
    totalReviews: 3035,
    newLabel: {
      enabled: false,
      content: 'NEW',
    },
    saleLabel: {
      enabled: false,
      content: 'SALE',
    },
    sizes: [
      '6',
      '7',
      '8',
      '8.5',
      '9',
      '9.5',
      '10',
      '10.5',
      '11',
      '11.5',
      '12',
      '13',
    ],
    subDescription:
      'Featuring the original ripple design inspired by Japanese bullet trains, the Nike Air Max 97 lets you push your style full-speed ahead.',
  },
  {
    id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b14',
    gender: ['Men'],
    images: [
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-1.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-2.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-3.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-4.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-5.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-6.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-7.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-8.webp',
    ],
    reviews: [
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b1',
        name: 'Jayvion Simon',
        postedAt: '2024-08-19T18:58:39+00:00',
        comment:
          'The sun slowly set over the horizon, painting the sky in vibrant hues of orange and pink.',
        isPurchased: true,
        rating: 4.2,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-1.webp',
        helpful: 9911,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b2',
        name: 'Lucian Obrien',
        postedAt: '2024-08-18T17:58:39+00:00',
        comment:
          'She eagerly opened the gift, her eyes sparkling with excitement.',
        isPurchased: true,
        rating: 3.7,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-2.webp',
        helpful: 1947,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-1.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b3',
        name: 'Deja Brady',
        postedAt: '2024-08-17T16:58:39+00:00',
        comment:
          'The old oak tree stood tall and majestic, its branches swaying gently in the breeze.',
        isPurchased: true,
        rating: 4.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-3.webp',
        helpful: 9124,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b4',
        name: 'Harrison Stein',
        postedAt: '2024-08-16T15:58:39+00:00',
        comment:
          'The aroma of freshly brewed coffee filled the air, awakening my senses.',
        isPurchased: false,
        rating: 3.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-4.webp',
        helpful: 6984,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-3.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-4.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b5',
        name: 'Reece Chung',
        postedAt: '2024-08-15T14:58:39+00:00',
        comment:
          'The children giggled with joy as they ran through the sprinklers on a hot summer day.',
        isPurchased: false,
        rating: 0.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-5.webp',
        helpful: 8488,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b6',
        name: 'Lainey Davidson',
        postedAt: '2024-08-14T13:58:39+00:00',
        comment:
          'He carefully crafted a beautiful sculpture out of clay, his hands skillfully shaping the intricate details.',
        isPurchased: true,
        rating: 3,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-6.webp',
        helpful: 2034,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-6.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-7.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-8.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b7',
        name: 'Cristopher Cardenas',
        postedAt: '2024-08-13T12:58:39+00:00',
        comment:
          'The concert was a mesmerizing experience, with the music filling the venue and the crowd cheering in delight.',
        isPurchased: false,
        rating: 2.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-7.webp',
        helpful: 3364,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b8',
        name: 'Melanie Noble',
        postedAt: '2024-08-12T11:58:39+00:00',
        comment:
          'The waves crashed against the shore, creating a soothing symphony of sound.',
        isPurchased: false,
        rating: 2.8,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-8.webp',
        helpful: 8401,
        attachments: [],
      },
    ],
    publish: 'published',
    ratings: [
      {
        name: '1 Star',
        starCount: 9911,
        reviewCount: 1947,
      },
      {
        name: '2 Star',
        starCount: 1947,
        reviewCount: 9124,
      },
      {
        name: '3 Star',
        starCount: 9124,
        reviewCount: 6984,
      },
      {
        name: '4 Star',
        starCount: 6984,
        reviewCount: 8488,
      },
      {
        name: '5 Star',
        starCount: 8488,
        reviewCount: 2034,
      },
    ],
    category: 'Shose',
    available: 72,
    priceSale: null,
    taxes: 10,
    quantity: 80,
    inventoryType: 'in stock',
    tags: [
      'Technology',
      'Health and Wellness',
      'Travel',
      'Finance',
      'Education',
    ],
    code: '38BEE2713',
    description:
      '\n<h6>Specifications</h6>\n<table>\n  <tbody>\n    <tr>\n      <td>Category</td>\n      <td>Mobile</td>\n    </tr>\n    <tr>\n      <td>Manufacturer</td>\n      <td>Apple</td>\n    </tr>\n    <tr>\n      <td>Warranty</td>\n      <td>12 Months</td>\n    </tr>\n    <tr>\n      <td>Serial number</td>\n      <td>358607726380311</td>\n    </tr>\n    <tr>\n      <td>Ships from</td>\n      <td>United States</td>\n    </tr>\n  </tbody>\n</table>\n\n<h6>Product details</h6>\n<ul>\n  <li>\n    <p>The foam sockliner feels soft and comfortable</p>\n  </li>\n  <li>\n    <p>Pull tab</p>\n  </li>\n  <li>\n    <p>Not intended for use as Personal Protective Equipment</p>\n  </li>\n  <li>\n    <p>Colour Shown: White/Black/Oxygen Purple/Action Grape</p>\n  </li>\n  <li>\n    <p>Style: 921826-109</p>\n  </li>\n  <li>\n    <p>Country/Region of Origin: China</p>\n  </li>\n</ul>\n<h6>Benefits</h6>\n<ul>\n  <li>\n    <p>Mesh and synthetic materials on the upper keep the fluid look of the OG while adding comfort</p>\n    and durability.\n  </li>\n  <li>\n    <p>Originally designed for performance running, the full-length Max Air unit adds soft, comfortable cushio</p>\n    ning underfoot.\n  </li>\n  <li>\n    <p>The foam midsole feels springy and soft.</p>\n  </li>\n  <li>\n    <p>The rubber outsole adds traction and durability.</p>\n  </li>\n</ul>\n<h6>Delivery and returns</h6>\n<p>Your order of $200 or more gets free standard delivery.</p>\n<ul>\n  <li>\n    <p>Standard delivered 4-5 Business Days</p>\n  </li>\n  <li>\n    <p>Express delivered 2-4 Business Days</p>\n  </li>\n</ul>\n<p>Orders are processed and delivered Monday-Friday (excluding public holidays)</p>\n\n',
    sku: 'WW75K52113YW/SV',
    createdAt: '2024-08-06T05:58:39+00:00',
    name: 'Stylish Wedge Heels',
    price: 77.32,
    coverUrl:
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-14.webp',
    colors: ['#FFC107', '#7F00FF', '#000000'],
    totalRatings: 2.8,
    totalSold: 394,
    totalReviews: 6733,
    newLabel: {
      enabled: false,
      content: 'NEW',
    },
    saleLabel: {
      enabled: false,
      content: 'SALE',
    },
    sizes: [
      '6',
      '7',
      '8',
      '8.5',
      '9',
      '9.5',
      '10',
      '10.5',
      '11',
      '11.5',
      '12',
      '13',
    ],
    subDescription:
      'Featuring the original ripple design inspired by Japanese bullet trains, the Nike Air Max 97 lets you push your style full-speed ahead.',
  },
  {
    id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b15',
    gender: ['Women', 'Kids'],
    images: [
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-1.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-2.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-3.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-4.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-5.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-6.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-7.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-8.webp',
    ],
    reviews: [
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b1',
        name: 'Jayvion Simon',
        postedAt: '2024-08-19T18:58:39+00:00',
        comment:
          'The sun slowly set over the horizon, painting the sky in vibrant hues of orange and pink.',
        isPurchased: true,
        rating: 4.2,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-1.webp',
        helpful: 9911,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b2',
        name: 'Lucian Obrien',
        postedAt: '2024-08-18T17:58:39+00:00',
        comment:
          'She eagerly opened the gift, her eyes sparkling with excitement.',
        isPurchased: true,
        rating: 3.7,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-2.webp',
        helpful: 1947,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-1.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b3',
        name: 'Deja Brady',
        postedAt: '2024-08-17T16:58:39+00:00',
        comment:
          'The old oak tree stood tall and majestic, its branches swaying gently in the breeze.',
        isPurchased: true,
        rating: 4.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-3.webp',
        helpful: 9124,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b4',
        name: 'Harrison Stein',
        postedAt: '2024-08-16T15:58:39+00:00',
        comment:
          'The aroma of freshly brewed coffee filled the air, awakening my senses.',
        isPurchased: false,
        rating: 3.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-4.webp',
        helpful: 6984,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-3.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-4.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b5',
        name: 'Reece Chung',
        postedAt: '2024-08-15T14:58:39+00:00',
        comment:
          'The children giggled with joy as they ran through the sprinklers on a hot summer day.',
        isPurchased: false,
        rating: 0.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-5.webp',
        helpful: 8488,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b6',
        name: 'Lainey Davidson',
        postedAt: '2024-08-14T13:58:39+00:00',
        comment:
          'He carefully crafted a beautiful sculpture out of clay, his hands skillfully shaping the intricate details.',
        isPurchased: true,
        rating: 3,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-6.webp',
        helpful: 2034,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-6.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-7.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-8.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b7',
        name: 'Cristopher Cardenas',
        postedAt: '2024-08-13T12:58:39+00:00',
        comment:
          'The concert was a mesmerizing experience, with the music filling the venue and the crowd cheering in delight.',
        isPurchased: false,
        rating: 2.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-7.webp',
        helpful: 3364,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b8',
        name: 'Melanie Noble',
        postedAt: '2024-08-12T11:58:39+00:00',
        comment:
          'The waves crashed against the shore, creating a soothing symphony of sound.',
        isPurchased: false,
        rating: 2.8,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-8.webp',
        helpful: 8401,
        attachments: [],
      },
    ],
    publish: 'published',
    ratings: [
      {
        name: '1 Star',
        starCount: 9911,
        reviewCount: 1947,
      },
      {
        name: '2 Star',
        starCount: 1947,
        reviewCount: 9124,
      },
      {
        name: '3 Star',
        starCount: 9124,
        reviewCount: 6984,
      },
      {
        name: '4 Star',
        starCount: 6984,
        reviewCount: 8488,
      },
      {
        name: '5 Star',
        starCount: 8488,
        reviewCount: 2034,
      },
    ],
    category: 'Apparel',
    available: 10,
    priceSale: null,
    taxes: 10,
    quantity: 80,
    inventoryType: 'low stock',
    tags: [
      'Technology',
      'Health and Wellness',
      'Travel',
      'Finance',
      'Education',
    ],
    code: '38BEE2714',
    description:
      '\n<h6>Specifications</h6>\n<table>\n  <tbody>\n    <tr>\n      <td>Category</td>\n      <td>Mobile</td>\n    </tr>\n    <tr>\n      <td>Manufacturer</td>\n      <td>Apple</td>\n    </tr>\n    <tr>\n      <td>Warranty</td>\n      <td>12 Months</td>\n    </tr>\n    <tr>\n      <td>Serial number</td>\n      <td>358607726380311</td>\n    </tr>\n    <tr>\n      <td>Ships from</td>\n      <td>United States</td>\n    </tr>\n  </tbody>\n</table>\n\n<h6>Product details</h6>\n<ul>\n  <li>\n    <p>The foam sockliner feels soft and comfortable</p>\n  </li>\n  <li>\n    <p>Pull tab</p>\n  </li>\n  <li>\n    <p>Not intended for use as Personal Protective Equipment</p>\n  </li>\n  <li>\n    <p>Colour Shown: White/Black/Oxygen Purple/Action Grape</p>\n  </li>\n  <li>\n    <p>Style: 921826-109</p>\n  </li>\n  <li>\n    <p>Country/Region of Origin: China</p>\n  </li>\n</ul>\n<h6>Benefits</h6>\n<ul>\n  <li>\n    <p>Mesh and synthetic materials on the upper keep the fluid look of the OG while adding comfort</p>\n    and durability.\n  </li>\n  <li>\n    <p>Originally designed for performance running, the full-length Max Air unit adds soft, comfortable cushio</p>\n    ning underfoot.\n  </li>\n  <li>\n    <p>The foam midsole feels springy and soft.</p>\n  </li>\n  <li>\n    <p>The rubber outsole adds traction and durability.</p>\n  </li>\n</ul>\n<h6>Delivery and returns</h6>\n<p>Your order of $200 or more gets free standard delivery.</p>\n<ul>\n  <li>\n    <p>Standard delivered 4-5 Business Days</p>\n  </li>\n  <li>\n    <p>Express delivered 2-4 Business Days</p>\n  </li>\n</ul>\n<p>Orders are processed and delivered Monday-Friday (excluding public holidays)</p>\n\n',
    sku: 'WW75K52114YW/SV',
    createdAt: '2024-08-05T04:58:39+00:00',
    name: 'Lightweight Training Shoes',
    price: 60.62,
    coverUrl:
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-15.webp',
    colors: ['#FF4842', '#1890FF'],
    totalRatings: 4.1,
    totalSold: 269,
    totalReviews: 3952,
    newLabel: {
      enabled: false,
      content: 'NEW',
    },
    saleLabel: {
      enabled: false,
      content: 'SALE',
    },
    sizes: [
      '6',
      '7',
      '8',
      '8.5',
      '9',
      '9.5',
      '10',
      '10.5',
      '11',
      '11.5',
      '12',
      '13',
    ],
    subDescription:
      'Featuring the original ripple design inspired by Japanese bullet trains, the Nike Air Max 97 lets you push your style full-speed ahead.',
  },
  {
    id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b16',
    gender: ['Men'],
    images: [
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-1.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-2.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-3.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-4.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-5.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-6.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-7.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-8.webp',
    ],
    reviews: [
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b1',
        name: 'Jayvion Simon',
        postedAt: '2024-08-19T18:58:39+00:00',
        comment:
          'The sun slowly set over the horizon, painting the sky in vibrant hues of orange and pink.',
        isPurchased: true,
        rating: 4.2,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-1.webp',
        helpful: 9911,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b2',
        name: 'Lucian Obrien',
        postedAt: '2024-08-18T17:58:39+00:00',
        comment:
          'She eagerly opened the gift, her eyes sparkling with excitement.',
        isPurchased: true,
        rating: 3.7,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-2.webp',
        helpful: 1947,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-1.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b3',
        name: 'Deja Brady',
        postedAt: '2024-08-17T16:58:39+00:00',
        comment:
          'The old oak tree stood tall and majestic, its branches swaying gently in the breeze.',
        isPurchased: true,
        rating: 4.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-3.webp',
        helpful: 9124,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b4',
        name: 'Harrison Stein',
        postedAt: '2024-08-16T15:58:39+00:00',
        comment:
          'The aroma of freshly brewed coffee filled the air, awakening my senses.',
        isPurchased: false,
        rating: 3.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-4.webp',
        helpful: 6984,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-3.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-4.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b5',
        name: 'Reece Chung',
        postedAt: '2024-08-15T14:58:39+00:00',
        comment:
          'The children giggled with joy as they ran through the sprinklers on a hot summer day.',
        isPurchased: false,
        rating: 0.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-5.webp',
        helpful: 8488,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b6',
        name: 'Lainey Davidson',
        postedAt: '2024-08-14T13:58:39+00:00',
        comment:
          'He carefully crafted a beautiful sculpture out of clay, his hands skillfully shaping the intricate details.',
        isPurchased: true,
        rating: 3,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-6.webp',
        helpful: 2034,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-6.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-7.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-8.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b7',
        name: 'Cristopher Cardenas',
        postedAt: '2024-08-13T12:58:39+00:00',
        comment:
          'The concert was a mesmerizing experience, with the music filling the venue and the crowd cheering in delight.',
        isPurchased: false,
        rating: 2.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-7.webp',
        helpful: 3364,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b8',
        name: 'Melanie Noble',
        postedAt: '2024-08-12T11:58:39+00:00',
        comment:
          'The waves crashed against the shore, creating a soothing symphony of sound.',
        isPurchased: false,
        rating: 2.8,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-8.webp',
        helpful: 8401,
        attachments: [],
      },
    ],
    publish: 'draft',
    ratings: [
      {
        name: '1 Star',
        starCount: 9911,
        reviewCount: 1947,
      },
      {
        name: '2 Star',
        starCount: 1947,
        reviewCount: 9124,
      },
      {
        name: '3 Star',
        starCount: 9124,
        reviewCount: 6984,
      },
      {
        name: '4 Star',
        starCount: 6984,
        reviewCount: 8488,
      },
      {
        name: '5 Star',
        starCount: 8488,
        reviewCount: 2034,
      },
    ],
    category: 'Shose',
    available: 72,
    priceSale: 79.81,
    taxes: 10,
    quantity: 80,
    inventoryType: 'in stock',
    tags: [
      'Technology',
      'Health and Wellness',
      'Travel',
      'Finance',
      'Education',
    ],
    code: '38BEE2715',
    description:
      '\n<h6>Specifications</h6>\n<table>\n  <tbody>\n    <tr>\n      <td>Category</td>\n      <td>Mobile</td>\n    </tr>\n    <tr>\n      <td>Manufacturer</td>\n      <td>Apple</td>\n    </tr>\n    <tr>\n      <td>Warranty</td>\n      <td>12 Months</td>\n    </tr>\n    <tr>\n      <td>Serial number</td>\n      <td>358607726380311</td>\n    </tr>\n    <tr>\n      <td>Ships from</td>\n      <td>United States</td>\n    </tr>\n  </tbody>\n</table>\n\n<h6>Product details</h6>\n<ul>\n  <li>\n    <p>The foam sockliner feels soft and comfortable</p>\n  </li>\n  <li>\n    <p>Pull tab</p>\n  </li>\n  <li>\n    <p>Not intended for use as Personal Protective Equipment</p>\n  </li>\n  <li>\n    <p>Colour Shown: White/Black/Oxygen Purple/Action Grape</p>\n  </li>\n  <li>\n    <p>Style: 921826-109</p>\n  </li>\n  <li>\n    <p>Country/Region of Origin: China</p>\n  </li>\n</ul>\n<h6>Benefits</h6>\n<ul>\n  <li>\n    <p>Mesh and synthetic materials on the upper keep the fluid look of the OG while adding comfort</p>\n    and durability.\n  </li>\n  <li>\n    <p>Originally designed for performance running, the full-length Max Air unit adds soft, comfortable cushio</p>\n    ning underfoot.\n  </li>\n  <li>\n    <p>The foam midsole feels springy and soft.</p>\n  </li>\n  <li>\n    <p>The rubber outsole adds traction and durability.</p>\n  </li>\n</ul>\n<h6>Delivery and returns</h6>\n<p>Your order of $200 or more gets free standard delivery.</p>\n<ul>\n  <li>\n    <p>Standard delivered 4-5 Business Days</p>\n  </li>\n  <li>\n    <p>Express delivered 2-4 Business Days</p>\n  </li>\n</ul>\n<p>Orders are processed and delivered Monday-Friday (excluding public holidays)</p>\n\n',
    sku: 'WW75K52115YW/SV',
    createdAt: '2024-08-04T03:58:39+00:00',
    name: 'Luxurious Moccasins',
    price: 79.81,
    coverUrl:
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-16.webp',
    colors: ['#7F00FF', '#000000', '#FFFFFF'],
    totalRatings: 4.5,
    totalSold: 453,
    totalReviews: 2405,
    newLabel: {
      enabled: false,
      content: 'NEW',
    },
    saleLabel: {
      enabled: false,
      content: 'SALE',
    },
    sizes: [
      '6',
      '7',
      '8',
      '8.5',
      '9',
      '9.5',
      '10',
      '10.5',
      '11',
      '11.5',
      '12',
      '13',
    ],
    subDescription:
      'Featuring the original ripple design inspired by Japanese bullet trains, the Nike Air Max 97 lets you push your style full-speed ahead.',
  },
  {
    id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b17',
    gender: ['Women', 'Kids'],
    images: [
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-1.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-2.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-3.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-4.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-5.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-6.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-7.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-8.webp',
    ],
    reviews: [
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b1',
        name: 'Jayvion Simon',
        postedAt: '2024-08-19T18:58:39+00:00',
        comment:
          'The sun slowly set over the horizon, painting the sky in vibrant hues of orange and pink.',
        isPurchased: true,
        rating: 4.2,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-1.webp',
        helpful: 9911,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b2',
        name: 'Lucian Obrien',
        postedAt: '2024-08-18T17:58:39+00:00',
        comment:
          'She eagerly opened the gift, her eyes sparkling with excitement.',
        isPurchased: true,
        rating: 3.7,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-2.webp',
        helpful: 1947,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-1.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b3',
        name: 'Deja Brady',
        postedAt: '2024-08-17T16:58:39+00:00',
        comment:
          'The old oak tree stood tall and majestic, its branches swaying gently in the breeze.',
        isPurchased: true,
        rating: 4.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-3.webp',
        helpful: 9124,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b4',
        name: 'Harrison Stein',
        postedAt: '2024-08-16T15:58:39+00:00',
        comment:
          'The aroma of freshly brewed coffee filled the air, awakening my senses.',
        isPurchased: false,
        rating: 3.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-4.webp',
        helpful: 6984,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-3.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-4.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b5',
        name: 'Reece Chung',
        postedAt: '2024-08-15T14:58:39+00:00',
        comment:
          'The children giggled with joy as they ran through the sprinklers on a hot summer day.',
        isPurchased: false,
        rating: 0.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-5.webp',
        helpful: 8488,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b6',
        name: 'Lainey Davidson',
        postedAt: '2024-08-14T13:58:39+00:00',
        comment:
          'He carefully crafted a beautiful sculpture out of clay, his hands skillfully shaping the intricate details.',
        isPurchased: true,
        rating: 3,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-6.webp',
        helpful: 2034,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-6.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-7.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-8.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b7',
        name: 'Cristopher Cardenas',
        postedAt: '2024-08-13T12:58:39+00:00',
        comment:
          'The concert was a mesmerizing experience, with the music filling the venue and the crowd cheering in delight.',
        isPurchased: false,
        rating: 2.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-7.webp',
        helpful: 3364,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b8',
        name: 'Melanie Noble',
        postedAt: '2024-08-12T11:58:39+00:00',
        comment:
          'The waves crashed against the shore, creating a soothing symphony of sound.',
        isPurchased: false,
        rating: 2.8,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-8.webp',
        helpful: 8401,
        attachments: [],
      },
    ],
    publish: 'published',
    ratings: [
      {
        name: '1 Star',
        starCount: 9911,
        reviewCount: 1947,
      },
      {
        name: '2 Star',
        starCount: 1947,
        reviewCount: 9124,
      },
      {
        name: '3 Star',
        starCount: 9124,
        reviewCount: 6984,
      },
      {
        name: '4 Star',
        starCount: 6984,
        reviewCount: 8488,
      },
      {
        name: '5 Star',
        starCount: 8488,
        reviewCount: 2034,
      },
    ],
    category: 'Apparel',
    available: 10,
    priceSale: null,
    taxes: 10,
    quantity: 80,
    inventoryType: 'low stock',
    tags: [
      'Technology',
      'Health and Wellness',
      'Travel',
      'Finance',
      'Education',
    ],
    code: '38BEE2716',
    description:
      '\n<h6>Specifications</h6>\n<table>\n  <tbody>\n    <tr>\n      <td>Category</td>\n      <td>Mobile</td>\n    </tr>\n    <tr>\n      <td>Manufacturer</td>\n      <td>Apple</td>\n    </tr>\n    <tr>\n      <td>Warranty</td>\n      <td>12 Months</td>\n    </tr>\n    <tr>\n      <td>Serial number</td>\n      <td>358607726380311</td>\n    </tr>\n    <tr>\n      <td>Ships from</td>\n      <td>United States</td>\n    </tr>\n  </tbody>\n</table>\n\n<h6>Product details</h6>\n<ul>\n  <li>\n    <p>The foam sockliner feels soft and comfortable</p>\n  </li>\n  <li>\n    <p>Pull tab</p>\n  </li>\n  <li>\n    <p>Not intended for use as Personal Protective Equipment</p>\n  </li>\n  <li>\n    <p>Colour Shown: White/Black/Oxygen Purple/Action Grape</p>\n  </li>\n  <li>\n    <p>Style: 921826-109</p>\n  </li>\n  <li>\n    <p>Country/Region of Origin: China</p>\n  </li>\n</ul>\n<h6>Benefits</h6>\n<ul>\n  <li>\n    <p>Mesh and synthetic materials on the upper keep the fluid look of the OG while adding comfort</p>\n    and durability.\n  </li>\n  <li>\n    <p>Originally designed for performance running, the full-length Max Air unit adds soft, comfortable cushio</p>\n    ning underfoot.\n  </li>\n  <li>\n    <p>The foam midsole feels springy and soft.</p>\n  </li>\n  <li>\n    <p>The rubber outsole adds traction and durability.</p>\n  </li>\n</ul>\n<h6>Delivery and returns</h6>\n<p>Your order of $200 or more gets free standard delivery.</p>\n<ul>\n  <li>\n    <p>Standard delivered 4-5 Business Days</p>\n  </li>\n  <li>\n    <p>Express delivered 2-4 Business Days</p>\n  </li>\n</ul>\n<p>Orders are processed and delivered Monday-Friday (excluding public holidays)</p>\n\n',
    sku: 'WW75K52116YW/SV',
    createdAt: '2024-08-03T02:58:39+00:00',
    name: 'Durable Work Boots',
    price: 93.68,
    coverUrl:
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-17.webp',
    colors: ['#FFC107', '#7F00FF'],
    totalRatings: 2.2,
    totalSold: 821,
    totalReviews: 3127,
    newLabel: {
      enabled: false,
      content: 'NEW',
    },
    saleLabel: {
      enabled: false,
      content: 'SALE',
    },
    sizes: [
      '6',
      '7',
      '8',
      '8.5',
      '9',
      '9.5',
      '10',
      '10.5',
      '11',
      '11.5',
      '12',
      '13',
    ],
    subDescription:
      'Featuring the original ripple design inspired by Japanese bullet trains, the Nike Air Max 97 lets you push your style full-speed ahead.',
  },
  {
    id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b18',
    gender: ['Men'],
    images: [
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-1.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-2.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-3.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-4.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-5.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-6.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-7.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-8.webp',
    ],
    reviews: [
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b1',
        name: 'Jayvion Simon',
        postedAt: '2024-08-19T18:58:39+00:00',
        comment:
          'The sun slowly set over the horizon, painting the sky in vibrant hues of orange and pink.',
        isPurchased: true,
        rating: 4.2,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-1.webp',
        helpful: 9911,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b2',
        name: 'Lucian Obrien',
        postedAt: '2024-08-18T17:58:39+00:00',
        comment:
          'She eagerly opened the gift, her eyes sparkling with excitement.',
        isPurchased: true,
        rating: 3.7,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-2.webp',
        helpful: 1947,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-1.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b3',
        name: 'Deja Brady',
        postedAt: '2024-08-17T16:58:39+00:00',
        comment:
          'The old oak tree stood tall and majestic, its branches swaying gently in the breeze.',
        isPurchased: true,
        rating: 4.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-3.webp',
        helpful: 9124,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b4',
        name: 'Harrison Stein',
        postedAt: '2024-08-16T15:58:39+00:00',
        comment:
          'The aroma of freshly brewed coffee filled the air, awakening my senses.',
        isPurchased: false,
        rating: 3.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-4.webp',
        helpful: 6984,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-3.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-4.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b5',
        name: 'Reece Chung',
        postedAt: '2024-08-15T14:58:39+00:00',
        comment:
          'The children giggled with joy as they ran through the sprinklers on a hot summer day.',
        isPurchased: false,
        rating: 0.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-5.webp',
        helpful: 8488,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b6',
        name: 'Lainey Davidson',
        postedAt: '2024-08-14T13:58:39+00:00',
        comment:
          'He carefully crafted a beautiful sculpture out of clay, his hands skillfully shaping the intricate details.',
        isPurchased: true,
        rating: 3,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-6.webp',
        helpful: 2034,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-6.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-7.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-8.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b7',
        name: 'Cristopher Cardenas',
        postedAt: '2024-08-13T12:58:39+00:00',
        comment:
          'The concert was a mesmerizing experience, with the music filling the venue and the crowd cheering in delight.',
        isPurchased: false,
        rating: 2.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-7.webp',
        helpful: 3364,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b8',
        name: 'Melanie Noble',
        postedAt: '2024-08-12T11:58:39+00:00',
        comment:
          'The waves crashed against the shore, creating a soothing symphony of sound.',
        isPurchased: false,
        rating: 2.8,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-8.webp',
        helpful: 8401,
        attachments: [],
      },
    ],
    publish: 'published',
    ratings: [
      {
        name: '1 Star',
        starCount: 9911,
        reviewCount: 1947,
      },
      {
        name: '2 Star',
        starCount: 1947,
        reviewCount: 9124,
      },
      {
        name: '3 Star',
        starCount: 9124,
        reviewCount: 6984,
      },
      {
        name: '4 Star',
        starCount: 6984,
        reviewCount: 8488,
      },
      {
        name: '5 Star',
        starCount: 8488,
        reviewCount: 2034,
      },
    ],
    category: 'Shose',
    available: 72,
    priceSale: null,
    taxes: 10,
    quantity: 80,
    inventoryType: 'in stock',
    tags: [
      'Technology',
      'Health and Wellness',
      'Travel',
      'Finance',
      'Education',
    ],
    code: '38BEE2717',
    description:
      '\n<h6>Specifications</h6>\n<table>\n  <tbody>\n    <tr>\n      <td>Category</td>\n      <td>Mobile</td>\n    </tr>\n    <tr>\n      <td>Manufacturer</td>\n      <td>Apple</td>\n    </tr>\n    <tr>\n      <td>Warranty</td>\n      <td>12 Months</td>\n    </tr>\n    <tr>\n      <td>Serial number</td>\n      <td>358607726380311</td>\n    </tr>\n    <tr>\n      <td>Ships from</td>\n      <td>United States</td>\n    </tr>\n  </tbody>\n</table>\n\n<h6>Product details</h6>\n<ul>\n  <li>\n    <p>The foam sockliner feels soft and comfortable</p>\n  </li>\n  <li>\n    <p>Pull tab</p>\n  </li>\n  <li>\n    <p>Not intended for use as Personal Protective Equipment</p>\n  </li>\n  <li>\n    <p>Colour Shown: White/Black/Oxygen Purple/Action Grape</p>\n  </li>\n  <li>\n    <p>Style: 921826-109</p>\n  </li>\n  <li>\n    <p>Country/Region of Origin: China</p>\n  </li>\n</ul>\n<h6>Benefits</h6>\n<ul>\n  <li>\n    <p>Mesh and synthetic materials on the upper keep the fluid look of the OG while adding comfort</p>\n    and durability.\n  </li>\n  <li>\n    <p>Originally designed for performance running, the full-length Max Air unit adds soft, comfortable cushio</p>\n    ning underfoot.\n  </li>\n  <li>\n    <p>The foam midsole feels springy and soft.</p>\n  </li>\n  <li>\n    <p>The rubber outsole adds traction and durability.</p>\n  </li>\n</ul>\n<h6>Delivery and returns</h6>\n<p>Your order of $200 or more gets free standard delivery.</p>\n<ul>\n  <li>\n    <p>Standard delivered 4-5 Business Days</p>\n  </li>\n  <li>\n    <p>Express delivered 2-4 Business Days</p>\n  </li>\n</ul>\n<p>Orders are processed and delivered Monday-Friday (excluding public holidays)</p>\n\n',
    sku: 'WW75K52117YW/SV',
    createdAt: '2024-08-02T01:58:39+00:00',
    name: 'Trendy Platform Sneakers',
    price: 47.44,
    coverUrl:
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-18.webp',
    colors: ['#7F00FF'],
    totalRatings: 3.2,
    totalSold: 364,
    totalReviews: 6843,
    newLabel: {
      enabled: false,
      content: 'NEW',
    },
    saleLabel: {
      enabled: false,
      content: 'SALE',
    },
    sizes: [
      '6',
      '7',
      '8',
      '8.5',
      '9',
      '9.5',
      '10',
      '10.5',
      '11',
      '11.5',
      '12',
      '13',
    ],
    subDescription:
      'Featuring the original ripple design inspired by Japanese bullet trains, the Nike Air Max 97 lets you push your style full-speed ahead.',
  },
  {
    id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b19',
    gender: ['Kids'],
    images: [
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-1.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-2.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-3.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-4.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-5.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-6.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-7.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-8.webp',
    ],
    reviews: [
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b1',
        name: 'Jayvion Simon',
        postedAt: '2024-08-19T18:58:39+00:00',
        comment:
          'The sun slowly set over the horizon, painting the sky in vibrant hues of orange and pink.',
        isPurchased: true,
        rating: 4.2,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-1.webp',
        helpful: 9911,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b2',
        name: 'Lucian Obrien',
        postedAt: '2024-08-18T17:58:39+00:00',
        comment:
          'She eagerly opened the gift, her eyes sparkling with excitement.',
        isPurchased: true,
        rating: 3.7,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-2.webp',
        helpful: 1947,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-1.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b3',
        name: 'Deja Brady',
        postedAt: '2024-08-17T16:58:39+00:00',
        comment:
          'The old oak tree stood tall and majestic, its branches swaying gently in the breeze.',
        isPurchased: true,
        rating: 4.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-3.webp',
        helpful: 9124,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b4',
        name: 'Harrison Stein',
        postedAt: '2024-08-16T15:58:39+00:00',
        comment:
          'The aroma of freshly brewed coffee filled the air, awakening my senses.',
        isPurchased: false,
        rating: 3.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-4.webp',
        helpful: 6984,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-3.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-4.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b5',
        name: 'Reece Chung',
        postedAt: '2024-08-15T14:58:39+00:00',
        comment:
          'The children giggled with joy as they ran through the sprinklers on a hot summer day.',
        isPurchased: false,
        rating: 0.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-5.webp',
        helpful: 8488,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b6',
        name: 'Lainey Davidson',
        postedAt: '2024-08-14T13:58:39+00:00',
        comment:
          'He carefully crafted a beautiful sculpture out of clay, his hands skillfully shaping the intricate details.',
        isPurchased: true,
        rating: 3,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-6.webp',
        helpful: 2034,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-6.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-7.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-8.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b7',
        name: 'Cristopher Cardenas',
        postedAt: '2024-08-13T12:58:39+00:00',
        comment:
          'The concert was a mesmerizing experience, with the music filling the venue and the crowd cheering in delight.',
        isPurchased: false,
        rating: 2.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-7.webp',
        helpful: 3364,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b8',
        name: 'Melanie Noble',
        postedAt: '2024-08-12T11:58:39+00:00',
        comment:
          'The waves crashed against the shore, creating a soothing symphony of sound.',
        isPurchased: false,
        rating: 2.8,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-8.webp',
        helpful: 8401,
        attachments: [],
      },
    ],
    publish: 'draft',
    ratings: [
      {
        name: '1 Star',
        starCount: 9911,
        reviewCount: 1947,
      },
      {
        name: '2 Star',
        starCount: 1947,
        reviewCount: 9124,
      },
      {
        name: '3 Star',
        starCount: 9124,
        reviewCount: 6984,
      },
      {
        name: '4 Star',
        starCount: 6984,
        reviewCount: 8488,
      },
      {
        name: '5 Star',
        starCount: 8488,
        reviewCount: 2034,
      },
    ],
    category: 'Accessories',
    available: 0,
    priceSale: 76.24,
    taxes: 10,
    quantity: 80,
    inventoryType: 'out of stock',
    tags: [
      'Technology',
      'Health and Wellness',
      'Travel',
      'Finance',
      'Education',
    ],
    code: '38BEE2718',
    description:
      '\n<h6>Specifications</h6>\n<table>\n  <tbody>\n    <tr>\n      <td>Category</td>\n      <td>Mobile</td>\n    </tr>\n    <tr>\n      <td>Manufacturer</td>\n      <td>Apple</td>\n    </tr>\n    <tr>\n      <td>Warranty</td>\n      <td>12 Months</td>\n    </tr>\n    <tr>\n      <td>Serial number</td>\n      <td>358607726380311</td>\n    </tr>\n    <tr>\n      <td>Ships from</td>\n      <td>United States</td>\n    </tr>\n  </tbody>\n</table>\n\n<h6>Product details</h6>\n<ul>\n  <li>\n    <p>The foam sockliner feels soft and comfortable</p>\n  </li>\n  <li>\n    <p>Pull tab</p>\n  </li>\n  <li>\n    <p>Not intended for use as Personal Protective Equipment</p>\n  </li>\n  <li>\n    <p>Colour Shown: White/Black/Oxygen Purple/Action Grape</p>\n  </li>\n  <li>\n    <p>Style: 921826-109</p>\n  </li>\n  <li>\n    <p>Country/Region of Origin: China</p>\n  </li>\n</ul>\n<h6>Benefits</h6>\n<ul>\n  <li>\n    <p>Mesh and synthetic materials on the upper keep the fluid look of the OG while adding comfort</p>\n    and durability.\n  </li>\n  <li>\n    <p>Originally designed for performance running, the full-length Max Air unit adds soft, comfortable cushio</p>\n    ning underfoot.\n  </li>\n  <li>\n    <p>The foam midsole feels springy and soft.</p>\n  </li>\n  <li>\n    <p>The rubber outsole adds traction and durability.</p>\n  </li>\n</ul>\n<h6>Delivery and returns</h6>\n<p>Your order of $200 or more gets free standard delivery.</p>\n<ul>\n  <li>\n    <p>Standard delivered 4-5 Business Days</p>\n  </li>\n  <li>\n    <p>Express delivered 2-4 Business Days</p>\n  </li>\n</ul>\n<p>Orders are processed and delivered Monday-Friday (excluding public holidays)</p>\n\n',
    sku: 'WW75K52118YW/SV',
    createdAt: '2024-08-01T00:58:39+00:00',
    name: 'Cozy Winter Boots',
    price: 76.24,
    coverUrl:
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-19.webp',
    colors: ['#FF4842', '#1890FF'],
    totalRatings: 0.6,
    totalSold: 849,
    totalReviews: 4672,
    newLabel: {
      enabled: false,
      content: 'NEW',
    },
    saleLabel: {
      enabled: false,
      content: 'SALE',
    },
    sizes: [
      '6',
      '7',
      '8',
      '8.5',
      '9',
      '9.5',
      '10',
      '10.5',
      '11',
      '11.5',
      '12',
      '13',
    ],
    subDescription:
      'Featuring the original ripple design inspired by Japanese bullet trains, the Nike Air Max 97 lets you push your style full-speed ahead.',
  },
  {
    id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b20',
    gender: ['Men'],
    images: [
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-1.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-2.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-3.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-4.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-5.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-6.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-7.webp',
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-8.webp',
    ],
    reviews: [
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b1',
        name: 'Jayvion Simon',
        postedAt: '2024-08-19T18:58:39+00:00',
        comment:
          'The sun slowly set over the horizon, painting the sky in vibrant hues of orange and pink.',
        isPurchased: true,
        rating: 4.2,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-1.webp',
        helpful: 9911,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b2',
        name: 'Lucian Obrien',
        postedAt: '2024-08-18T17:58:39+00:00',
        comment:
          'She eagerly opened the gift, her eyes sparkling with excitement.',
        isPurchased: true,
        rating: 3.7,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-2.webp',
        helpful: 1947,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-1.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b3',
        name: 'Deja Brady',
        postedAt: '2024-08-17T16:58:39+00:00',
        comment:
          'The old oak tree stood tall and majestic, its branches swaying gently in the breeze.',
        isPurchased: true,
        rating: 4.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-3.webp',
        helpful: 9124,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b4',
        name: 'Harrison Stein',
        postedAt: '2024-08-16T15:58:39+00:00',
        comment:
          'The aroma of freshly brewed coffee filled the air, awakening my senses.',
        isPurchased: false,
        rating: 3.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-4.webp',
        helpful: 6984,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-3.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-4.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b5',
        name: 'Reece Chung',
        postedAt: '2024-08-15T14:58:39+00:00',
        comment:
          'The children giggled with joy as they ran through the sprinklers on a hot summer day.',
        isPurchased: false,
        rating: 0.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-5.webp',
        helpful: 8488,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b6',
        name: 'Lainey Davidson',
        postedAt: '2024-08-14T13:58:39+00:00',
        comment:
          'He carefully crafted a beautiful sculpture out of clay, his hands skillfully shaping the intricate details.',
        isPurchased: true,
        rating: 3,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-6.webp',
        helpful: 2034,
        attachments: [
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-6.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-7.webp',
          'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-8.webp',
        ],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b7',
        name: 'Cristopher Cardenas',
        postedAt: '2024-08-13T12:58:39+00:00',
        comment:
          'The concert was a mesmerizing experience, with the music filling the venue and the crowd cheering in delight.',
        isPurchased: false,
        rating: 2.5,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-7.webp',
        helpful: 3364,
        attachments: [],
      },
      {
        id: 'e99f09a7-dd88-49d5-b1c8-1daf80c2d7b8',
        name: 'Melanie Noble',
        postedAt: '2024-08-12T11:58:39+00:00',
        comment:
          'The waves crashed against the shore, creating a soothing symphony of sound.',
        isPurchased: false,
        rating: 2.8,
        avatarUrl:
          'https://api-dev-minimal-v610.pages.dev/assets/images/avatar/avatar-8.webp',
        helpful: 8401,
        attachments: [],
      },
    ],
    publish: 'published',
    ratings: [
      {
        name: '1 Star',
        starCount: 9911,
        reviewCount: 1947,
      },
      {
        name: '2 Star',
        starCount: 1947,
        reviewCount: 9124,
      },
      {
        name: '3 Star',
        starCount: 9124,
        reviewCount: 6984,
      },
      {
        name: '4 Star',
        starCount: 6984,
        reviewCount: 8488,
      },
      {
        name: '5 Star',
        starCount: 8488,
        reviewCount: 2034,
      },
    ],
    category: 'Shose',
    available: 72,
    priceSale: null,
    taxes: 10,
    quantity: 80,
    inventoryType: 'in stock',
    tags: [
      'Technology',
      'Health and Wellness',
      'Travel',
      'Finance',
      'Education',
    ],
    code: '38BEE2719',
    description:
      '\n<h6>Specifications</h6>\n<table>\n  <tbody>\n    <tr>\n      <td>Category</td>\n      <td>Mobile</td>\n    </tr>\n    <tr>\n      <td>Manufacturer</td>\n      <td>Apple</td>\n    </tr>\n    <tr>\n      <td>Warranty</td>\n      <td>12 Months</td>\n    </tr>\n    <tr>\n      <td>Serial number</td>\n      <td>358607726380311</td>\n    </tr>\n    <tr>\n      <td>Ships from</td>\n      <td>United States</td>\n    </tr>\n  </tbody>\n</table>\n\n<h6>Product details</h6>\n<ul>\n  <li>\n    <p>The foam sockliner feels soft and comfortable</p>\n  </li>\n  <li>\n    <p>Pull tab</p>\n  </li>\n  <li>\n    <p>Not intended for use as Personal Protective Equipment</p>\n  </li>\n  <li>\n    <p>Colour Shown: White/Black/Oxygen Purple/Action Grape</p>\n  </li>\n  <li>\n    <p>Style: 921826-109</p>\n  </li>\n  <li>\n    <p>Country/Region of Origin: China</p>\n  </li>\n</ul>\n<h6>Benefits</h6>\n<ul>\n  <li>\n    <p>Mesh and synthetic materials on the upper keep the fluid look of the OG while adding comfort</p>\n    and durability.\n  </li>\n  <li>\n    <p>Originally designed for performance running, the full-length Max Air unit adds soft, comfortable cushio</p>\n    ning underfoot.\n  </li>\n  <li>\n    <p>The foam midsole feels springy and soft.</p>\n  </li>\n  <li>\n    <p>The rubber outsole adds traction and durability.</p>\n  </li>\n</ul>\n<h6>Delivery and returns</h6>\n<p>Your order of $200 or more gets free standard delivery.</p>\n<ul>\n  <li>\n    <p>Standard delivered 4-5 Business Days</p>\n  </li>\n  <li>\n    <p>Express delivered 2-4 Business Days</p>\n  </li>\n</ul>\n<p>Orders are processed and delivered Monday-Friday (excluding public holidays)</p>\n\n',
    sku: 'WW75K52119YW/SV',
    createdAt: '2024-07-30T23:58:39+00:00',
    name: 'Fashion Ankle Boots',
    price: 92.87,
    coverUrl:
      'https://api-dev-minimal-v610.pages.dev/assets/images/m-product/product-20.webp',
    colors: ['#FFC107', '#7F00FF'],
    totalRatings: 1.3,
    totalSold: 804,
    totalReviews: 6995,
    newLabel: {
      enabled: false,
      content: 'NEW',
    },
    saleLabel: {
      enabled: false,
      content: 'SALE',
    },
    sizes: [
      '6',
      '7',
      '8',
      '8.5',
      '9',
      '9.5',
      '10',
      '10.5',
      '11',
      '11.5',
      '12',
      '13',
    ],
    subDescription:
      'Featuring the original ripple design inspired by Japanese bullet trains, the Nike Air Max 97 lets you push your style full-speed ahead.',
  },
];
