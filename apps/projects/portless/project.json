{"name": "projects-portless", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "tags": ["firebase:app", "firebase:name:projects-portless"], "implicitDependencies": ["apps-admin-api", "apps-seller-portal-api", "services-17track", "services-aftership", "services-mabang", "services-oms", "services-organization", "services-portless", "services-zonos"], "targets": {"build": {"executor": "nx:run-commands", "dependsOn": ["^build"], "options": {"command": "echo Build succeeded."}}, "watch": {"executor": "nx:run-commands", "options": {"command": "nx run-many --targets=build --projects=tag:firebase:dep:projects-portless --parallel=100 --watch"}}, "lint": {"executor": "nx:run-commands", "options": {"command": "nx run-many --targets=lint --projects=tag:firebase:dep:projects-portless --parallel=100"}}, "test": {"executor": "nx:run-commands", "options": {"command": "nx run-many --targets=test --projects=tag:firebase:dep:projects-portless --parallel=100"}}, "firebase": {"executor": "nx:run-commands", "options": {"command": "firebase --config=firebase.json"}, "configurations": {"production": {"command": "firebase --config=firebase.json"}}}, "killports": {"executor": "nx:run-commands", "options": {"command": "kill-port --port 9099,5001,8080,9000,5100,8085,9199,9299,4000,4400,4500"}}, "getconfig": {"executor": "nx:run-commands", "options": {"command": "nx run projects-portless:firebase functions:config:get > apps/projects/portless/environment/.runtimeconfig.json"}}, "emulate": {"executor": "nx:run-commands", "options": {"commands": ["nx run projects-portless:killports", "nx run projects-portless:firebase emulators:start --import=apps/projects/portless/.emulators --export-on-exit"], "parallel": false}}, "serve": {"executor": "@simondotm/nx-firebase:serve", "options": {"commands": ["nx run projects-portless:watch", "nx run projects-portless:emulate"]}}, "deploy": {"executor": "nx:run-commands", "dependsOn": ["build"], "options": {"command": "nx run projects-portless:firebase deploy --only firestore:rules"}}}}