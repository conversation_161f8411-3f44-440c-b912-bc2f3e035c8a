{"indexes": [{"collectionGroup": "ActivityLog", "queryScope": "COLLECTION", "fields": [{"fieldPath": "createdAt", "order": "ASCENDING"}, {"fieldPath": "message", "order": "ASCENDING"}]}, {"collectionGroup": "ActivityLog", "queryScope": "COLLECTION", "fields": [{"fieldPath": "createdAt", "order": "DESCENDING"}, {"fieldPath": "message", "order": "DESCENDING"}]}, {"collectionGroup": "ActivityLog", "queryScope": "COLLECTION", "fields": [{"fieldPath": "createdAt", "order": "DESCENDING"}, {"fieldPath": "message", "order": "DESCENDING"}, {"fieldPath": "storeID", "order": "DESCENDING"}, {"fieldPath": "type", "order": "DESCENDING"}]}, {"collectionGroup": "ActivityLog", "queryScope": "COLLECTION", "fields": [{"fieldPath": "createdAt", "order": "DESCENDING"}, {"fieldPath": "message", "order": "DESCENDING"}, {"fieldPath": "type", "order": "DESCENDING"}]}, {"collectionGroup": "ActivityLog", "queryScope": "COLLECTION", "fields": [{"fieldPath": "createdAt", "order": "DESCENDING"}, {"fieldPath": "storeID", "order": "DESCENDING"}]}, {"collectionGroup": "ActivityLog", "queryScope": "COLLECTION", "fields": [{"fieldPath": "createdAt", "order": "DESCENDING"}, {"fieldPath": "storeID", "order": "DESCENDING"}, {"fieldPath": "type", "order": "DESCENDING"}]}, {"collectionGroup": "ActivityLog", "queryScope": "COLLECTION", "fields": [{"fieldPath": "createdAt", "order": "DESCENDING"}, {"fieldPath": "type", "order": "DESCENDING"}]}, {"collectionGroup": "ActivityLog", "queryScope": "COLLECTION", "fields": [{"fieldPath": "message", "order": "ASCENDING"}, {"fieldPath": "storeID", "order": "ASCENDING"}]}, {"collectionGroup": "ActivityLog", "queryScope": "COLLECTION", "fields": [{"fieldPath": "message", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}]}, {"collectionGroup": "ActivityLog", "queryScope": "COLLECTION", "fields": [{"fieldPath": "storeID", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}]}, {"collectionGroup": "ERPInventory", "queryScope": "COLLECTION", "fields": [{"fieldPath": "PlatformProductID", "order": "ASCENDING"}, {"fieldPath": "storeID", "order": "ASCENDING"}, {"fieldPath": "inventory", "order": "DESCENDING"}]}, {"collectionGroup": "PlatformOrder", "queryScope": "COLLECTION", "fields": [{"fieldPath": "orderCreatedAt", "order": "DESCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "PlatformOrder", "queryScope": "COLLECTION", "fields": [{"fieldPath": "orderCreatedAt", "order": "DESCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}, {"fieldPath": "fulfillStatus", "order": "DESCENDING"}]}, {"collectionGroup": "PlatformOrder", "queryScope": "COLLECTION", "fields": [{"fieldPath": "orderCreatedAt", "order": "DESCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}, {"fieldPath": "orderNumber", "order": "DESCENDING"}]}, {"collectionGroup": "PlatformOrder", "queryScope": "COLLECTION", "fields": [{"fieldPath": "orderCreatedAt", "order": "DESCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}, {"fieldPath": "storeID", "order": "DESCENDING"}]}, {"collectionGroup": "PlatformOrder", "queryScope": "COLLECTION", "fields": [{"fieldPath": "orderCreatedAt", "order": "DESCENDING"}, {"fieldPath": "fulfillStatus", "order": "DESCENDING"}]}, {"collectionGroup": "PlatformOrder", "queryScope": "COLLECTION", "fields": [{"fieldPath": "orderCreatedAt", "order": "DESCENDING"}, {"fieldPath": "fulfillStatus", "order": "DESCENDING"}, {"fieldPath": "orderNumber", "order": "DESCENDING"}]}, {"collectionGroup": "PlatformOrder", "queryScope": "COLLECTION", "fields": [{"fieldPath": "orderCreatedAt", "order": "DESCENDING"}, {"fieldPath": "fulfillStatus", "order": "DESCENDING"}, {"fieldPath": "orderNumber", "order": "DESCENDING"}, {"fieldPath": "store", "order": "DESCENDING"}]}, {"collectionGroup": "PlatformOrder", "queryScope": "COLLECTION", "fields": [{"fieldPath": "orderCreatedAt", "order": "DESCENDING"}, {"fieldPath": "fulfillStatus", "order": "DESCENDING"}, {"fieldPath": "store", "order": "DESCENDING"}]}, {"collectionGroup": "PlatformOrder", "queryScope": "COLLECTION", "fields": [{"fieldPath": "orderCreatedAt", "order": "DESCENDING"}, {"fieldPath": "fulfillStatus", "order": "DESCENDING"}, {"fieldPath": "storeID", "order": "DESCENDING"}]}, {"collectionGroup": "PlatformOrder", "queryScope": "COLLECTION", "fields": [{"fieldPath": "orderCreatedAt", "order": "DESCENDING"}, {"fieldPath": "orderNumber", "order": "DESCENDING"}]}, {"collectionGroup": "PlatformOrder", "queryScope": "COLLECTION", "fields": [{"fieldPath": "orderCreatedAt", "order": "DESCENDING"}, {"fieldPath": "orderNumber", "order": "DESCENDING"}, {"fieldPath": "store", "order": "DESCENDING"}]}, {"collectionGroup": "PlatformOrder", "queryScope": "COLLECTION", "fields": [{"fieldPath": "orderCreatedAt", "order": "DESCENDING"}, {"fieldPath": "store", "order": "DESCENDING"}]}, {"collectionGroup": "PlatformOrder", "queryScope": "COLLECTION", "fields": [{"fieldPath": "orderCreatedAt", "order": "DESCENDING"}, {"fieldPath": "storeID", "order": "DESCENDING"}]}, {"collectionGroup": "PlatformOrder", "queryScope": "COLLECTION", "fields": [{"fieldPath": "storeID", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}]}, {"collectionGroup": "PlatformOrder", "queryScope": "COLLECTION", "fields": [{"fieldPath": "storeID", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "PlatformOrder", "queryScope": "COLLECTION", "fields": [{"fieldPath": "storeID", "order": "ASCENDING"}, {"fieldPath": "orderCreatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "PlatformOrder", "queryScope": "COLLECTION", "fields": [{"fieldPath": "storeID", "order": "ASCENDING"}, {"fieldPath": "orderCreatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "PlatformOrder", "queryScope": "COLLECTION", "fields": [{"fieldPath": "storeID", "order": "ASCENDING"}, {"fieldPath": "orderCreatedAt", "order": "DESCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "PlatformOrder", "queryScope": "COLLECTION", "fields": [{"fieldPath": "storeID", "order": "ASCENDING"}, {"fieldPath": "orderCreatedAt", "order": "DESCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}, {"fieldPath": "orderNumber", "order": "DESCENDING"}]}, {"collectionGroup": "PlatformOrder", "queryScope": "COLLECTION", "fields": [{"fieldPath": "storeID", "order": "ASCENDING"}, {"fieldPath": "orderCreatedAt", "order": "DESCENDING"}, {"fieldPath": "fulfillStatus", "order": "DESCENDING"}]}, {"collectionGroup": "PlatformOrder", "queryScope": "COLLECTION", "fields": [{"fieldPath": "storeID", "order": "ASCENDING"}, {"fieldPath": "orderCreatedAt", "order": "DESCENDING"}, {"fieldPath": "fulfillStatus", "order": "DESCENDING"}, {"fieldPath": "orderNumber", "order": "DESCENDING"}]}, {"collectionGroup": "PlatformOrder", "queryScope": "COLLECTION", "fields": [{"fieldPath": "storeID", "order": "ASCENDING"}, {"fieldPath": "orderCreatedAt", "order": "DESCENDING"}, {"fieldPath": "orderNumber", "order": "DESCENDING"}]}, {"collectionGroup": "PlatformOrder", "queryScope": "COLLECTION", "fields": [{"fieldPath": "storeID", "order": "ASCENDING"}, {"fieldPath": "orderDate", "order": "ASCENDING"}]}, {"collectionGroup": "PlatformProduct", "queryScope": "COLLECTION", "fields": [{"fieldPath": "productTitle", "order": "DESCENDING"}, {"fieldPath": "sku", "order": "DESCENDING"}]}, {"collectionGroup": "PlatformProduct", "queryScope": "COLLECTION", "fields": [{"fieldPath": "productTitle", "order": "DESCENDING"}, {"fieldPath": "sku", "order": "DESCENDING"}, {"fieldPath": "status", "order": "DESCENDING"}]}, {"collectionGroup": "PlatformProduct", "queryScope": "COLLECTION", "fields": [{"fieldPath": "productTitle", "order": "DESCENDING"}, {"fieldPath": "sku", "order": "DESCENDING"}, {"fieldPath": "status", "order": "DESCENDING"}, {"fieldPath": "store", "order": "DESCENDING"}]}, {"collectionGroup": "PlatformProduct", "queryScope": "COLLECTION", "fields": [{"fieldPath": "productTitle", "order": "DESCENDING"}, {"fieldPath": "sku", "order": "DESCENDING"}, {"fieldPath": "storeID", "order": "DESCENDING"}]}, {"collectionGroup": "PlatformProduct", "queryScope": "COLLECTION", "fields": [{"fieldPath": "productTitle", "order": "DESCENDING"}, {"fieldPath": "status", "order": "DESCENDING"}]}, {"collectionGroup": "PlatformProduct", "queryScope": "COLLECTION", "fields": [{"fieldPath": "productTitle", "order": "DESCENDING"}, {"fieldPath": "status", "order": "DESCENDING"}, {"fieldPath": "store", "order": "DESCENDING"}]}, {"collectionGroup": "PlatformProduct", "queryScope": "COLLECTION", "fields": [{"fieldPath": "productTitle", "order": "DESCENDING"}, {"fieldPath": "status", "order": "DESCENDING"}, {"fieldPath": "storeID", "order": "DESCENDING"}]}, {"collectionGroup": "PlatformProduct", "queryScope": "COLLECTION", "fields": [{"fieldPath": "productTitle", "order": "DESCENDING"}, {"fieldPath": "store", "order": "DESCENDING"}]}, {"collectionGroup": "PlatformProduct", "queryScope": "COLLECTION", "fields": [{"fieldPath": "productTitle", "order": "DESCENDING"}, {"fieldPath": "storeID", "order": "DESCENDING"}]}, {"collectionGroup": "PlatformProduct", "queryScope": "COLLECTION", "fields": [{"fieldPath": "sku", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "storeID", "order": "ASCENDING"}]}, {"collectionGroup": "PlatformProduct", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "sku", "order": "ASCENDING"}]}, {"collectionGroup": "PlatformProduct", "queryScope": "COLLECTION", "fields": [{"fieldPath": "storeID", "order": "ASCENDING"}, {"fieldPath": "productTitle", "order": "ASCENDING"}]}, {"collectionGroup": "PlatformProduct", "queryScope": "COLLECTION", "fields": [{"fieldPath": "storeID", "order": "ASCENDING"}, {"fieldPath": "productTitle", "order": "DESCENDING"}]}, {"collectionGroup": "PlatformProduct", "queryScope": "COLLECTION", "fields": [{"fieldPath": "storeID", "order": "ASCENDING"}, {"fieldPath": "productTitle", "order": "DESCENDING"}, {"fieldPath": "sku", "order": "DESCENDING"}]}, {"collectionGroup": "PlatformProduct", "queryScope": "COLLECTION", "fields": [{"fieldPath": "storeID", "order": "ASCENDING"}, {"fieldPath": "productTitle", "order": "DESCENDING"}, {"fieldPath": "sku", "order": "DESCENDING"}, {"fieldPath": "status", "order": "DESCENDING"}]}, {"collectionGroup": "PlatformProduct", "queryScope": "COLLECTION", "fields": [{"fieldPath": "storeID", "order": "ASCENDING"}, {"fieldPath": "productTitle", "order": "DESCENDING"}, {"fieldPath": "status", "order": "DESCENDING"}]}], "fieldOverrides": []}