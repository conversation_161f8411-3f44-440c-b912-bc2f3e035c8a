rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    match /integrations/{document=**} {
      allow read: if request.auth != null;
    }
    match /global-settings/{document=**} {
      allow read: if request.auth != null;
    }
    match /organizations/{organization_id}/{document=**} {
      allow read: if request.auth != null && request.auth.token.organization_id == organization_id;
      allow read: if request.auth != null && request.auth.token.role == 'internal';
      allow read,write: if request.auth != null && request.auth.token.role == 'admin';
    }
  }
}

