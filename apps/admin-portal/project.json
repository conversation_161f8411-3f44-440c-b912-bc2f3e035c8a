{"name": "admin-portal", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/admin-portal/src", "projectType": "application", "tags": [], "targets": {"build": {"configurations": {"development": {"mode": "development"}, "staging": {"mode": "staging"}, "production": {"mode": "production"}}}, "test": {"executor": "@nx/vite:test", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"]}}}