/**
 * @desc Will update the undefined value to the default value passed. Falls back to empty string ""
 * @param obj
 */
export function sanitizeUndefined(value: any, defaultValue: any = ''): any {
  if (Array.isArray(value)) {
    return value.map(sanitizeUndefined);
  }

  if (value !== null && typeof value === 'object') {
    return Object.fromEntries(
      Object.entries(value).map(([key, val]) => [key, sanitizeUndefined(val)])
    );
  }

  return value === undefined ? defaultValue : value;
}
