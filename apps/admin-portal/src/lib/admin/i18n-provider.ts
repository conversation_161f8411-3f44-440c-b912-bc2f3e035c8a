import polyglotI18nProvider from 'ra-i18n-polyglot';
import en from 'ra-language-english';

const translations = {
  en: {
    ...en,
    ra: {
      ...en.ra,
      action: {
        ...en.ra.action,
        show: 'View',
      },
    },
    resources: {
      organizations: {
        name: 'Merchant  |||| Merchants',
        fields: {
          name: 'Name',
          settings: {
            products: {
              low_inventory_threshold: 'Low inventory threshold',
            },
          },
        },
      },
      carriersGroups: {
        name: 'Carrier Groups  |||| Carrier Groups',
      },
      trackingServices: {
        name: 'Tracking Services  |||| Tracking Services',
      },
      locations: {
        fields: {
          address: {
            line_1: 'Address',
            city: 'City',
            state: 'State',
            country_code: 'Country',
          },
        },
      },
    },
  },
};

export const i18nProvider = polyglotI18nProvider(
  (locale) => translations[locale],
  'en', // default locale
  [{ locale: 'en', name: 'English' }]
);
