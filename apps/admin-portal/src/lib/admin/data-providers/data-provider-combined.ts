import { combineDataProviders } from 'react-admin';

import { carrierDataProvider } from './carrier';
import { carrierGroupDataProvider } from './carrier-group';
import { carrierTrackingServiceDataProvider } from './carrier-tracking-service';
import { organizationDataProvider } from './organization';
import { trackingServiceDataProvider } from './tracking-service';

export const dataProviderCombined = combineDataProviders((resource) => {
  switch (resource) {
    case 'organizations':
    case 'locations':
    case 'users':
      return organizationDataProvider;
    case 'carriersGroups':
      return carrierGroupDataProvider;
    case 'carriers':
      return carrierDataProvider;
    case 'trackingServices':
      return trackingServiceDataProvider;
    case 'carrierTrackingServices':
      return carrierTrackingServiceDataProvider;
    default:
      throw new Error(`Unknown resource: ${resource}`);
  }
});
