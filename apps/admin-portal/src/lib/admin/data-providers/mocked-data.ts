// Temporary mocked data for the admin portal related to
// - Tracking Services
// - Carrier Groups
// - Carriers

export const TRACKING_SERVICES_MOCKED_DATA = [
  {
    id: '1',
    name: 'Aftership',
    apiUrl: 'https://api.aftership.com/v4',
    apiKey: '1234567890',
    carriers_on_trackingService: [
      {
        id: 1,
        name: 'Fedex Express',
        regex: '^[0-9]{15}$',
        trackingSamples: ['123456789012345', '123456789012346'],
        destinationCountries: ['United States', 'Canada'],
      },
      {
        id: 2,
        name: 'Fedex Ground',
        regex: '^[0-9]{15}$',
        trackingSamples: ['123456789012345', '123456789012346'],
        destinationCountries: ['United States', 'Canada'],
      },
    ],
  },
  {
    id: '2',
    name: '17Track',
    apiUrl: 'https://api.17track.net/v2',
    apiKey: '1234567890',
  },
  {
    id: '3',
    name: 'Carrier Website',
    apiUrl: 'no api url',
    apiKey: 'no api key',
  },
];

export const CARRIER_GROUP_MOCKED_DATA = [
  {
    id: '1',
    name: 'Fedex',
    description:
      'Fedex is a global shipping company that provides a wide range of shipping services.',
    trackingPageUrl: 'https://www.fedex.com/tracking',
    carriers_on_carrierGroup: [
      {
        id: '1',
        name: 'Fedex Express',
        regex: '^[0-9]{15}$',
        trackingSamples: ['123456789012345', '123456789012346'],
        destinationCountries: ['United States', 'Canada'],
      },
      {
        id: '2',
        name: 'Fedex Ground',
        regex: '^[0-9]{15}$',
        trackingSamples: ['123456789012345', '123456789012346'],
        destinationCountries: ['United States', 'Canada'],
      },
      {
        id: '3',
        name: 'Fedex International',
        regex: '^[0-9]{15}$',
        trackingSamples: ['123456789012345', '123456789012346'],
        destinationCountries: ['United States', 'Canada'],
      },
    ],
  },
  {
    id: '2',
    name: 'UPS',
    description:
      'UPS is a global shipping company that provides a wide range of shipping services.',
    trackingPageUrl: 'https://www.ups.com/tracking',
    carriers_on_carrierGroup: [
      {
        id: '4',
        name: 'UPS Express',
        regex: '^[0-9]{15}$',
        trackingSamples: ['123456789012345', '123456789012346'],
        destinationCountries: ['United States', 'Canada'],
      },
      {
        id: '5',
        name: 'UPS Ground',
        regex: '^[0-9]{15}$',
        trackingSamples: ['123456789012345', '123456789012346'],
        destinationCountries: ['United States', 'Canada'],
      },
      {
        id: '6',
        name: 'UPS International',
        regex: '^[0-9]{15}$',
        trackingSamples: ['123456789012345', '123456789012346'],
        destinationCountries: ['United States', 'Canada'],
      },
    ],
  },
  {
    id: '3',
    name: 'USPS',
    description:
      'USPS is a global shipping company that provides a wide range of shipping services.',
    trackingPageUrl: 'https://www.usps.com/tracking',
    carriers_on_carrierGroup: [
      {
        id: '7',
        name: 'USPS Express',
        regex: '^[0-9]{15}$',
        trackingSamples: ['123456789012345', '123456789012346'],
        destinationCountries: ['United States', 'Canada'],
      },
      {
        id: '8',
        name: 'USPS Ground',
        regex: '^[0-9]{15}$',
        trackingSamples: ['123456789012345', '123456789012346'],
        destinationCountries: ['United States', 'Canada'],
      },
      {
        id: '9',
        name: 'USPS International',
        regex: '^[0-9]{15}$',
        trackingSamples: ['123456789012345', '123456789012346'],
        destinationCountries: ['United States', 'Canada'],
      },
      {
        id: '10',
        name: 'USPS International',
        regex: '^[0-9]{15}$',
        trackingSamples: ['123456789012345', '123456789012346'],
        destinationCountries: ['United States', 'Canada'],
      },
    ],
  },
  {
    id: '4',
    name: 'DHL',
    description:
      'DHL is a global shipping company that provides a wide range of shipping services.',
    trackingPageUrl: 'https://www.dhl.com/tracking',
    carriers_on_carrierGroup: [
      {
        id: '11',
        name: 'DHL Express',
        regex: '^[0-9]{15}$',
        trackingSamples: ['123456789012345', '123456789012346'],
        destinationCountries: ['United States', 'Canada'],
      },
      {
        id: '12',
        name: 'DHL Ground',
        regex: '^[0-9]{15}$',
        trackingSamples: ['123456789012345', '123456789012346'],
        destinationCountries: ['United States', 'Canada'],
      },
      {
        id: '13',
        name: 'DHL International',
        regex: '^[0-9]{15}$',
        trackingSamples: ['123456789012345', '123456789012346'],
        destinationCountries: ['United States', 'Canada'],
      },
    ],
  },
];

export const CARRIER_MOCKED_DATA = [
  {
    id: '1',
    name: 'Fedex Express',
    regex: '^[0-9]{15}$',
    trackingSamples: ['123456789012345', '123456789012346'],
    destinationCountries: ['United States', 'Canada'],
  },
  {
    id: '2',
    name: 'UPS Express',
    regex: '^[0-9]{15}$',
    trackingSamples: ['123456789012345', '123456789012346'],
    destinationCountries: ['United States', 'Canada'],
  },
  {
    id: '3',
    name: 'USPS Express',
    regex: '^[0-9]{15}$',
    trackingSamples: ['123456789012345', '123456789012346'],
    destinationCountries: ['United States', 'Canada'],
  },
];

export const CARRIER_TRACKING_SERVICES_MOCKED_DATA = [
  {
    id: '1',
    carrierId: '1',
    trackingServiceId: '1',
    carriers_on_carrierTrackingService: [
      {
        id: '1',
        name: 'Fedex Express',
        regex: '^[0-9]{15}$',
        trackingSamples: ['123456789012345', '123456789012346'],
        destinationCountries: ['United States', 'Canada'],
      },
      {
        id: '2',
        name: 'Fedex Ground',
        regex: '^[0-9]{15}$',
        trackingSamples: ['123456789012345', '123456789012346'],
        destinationCountries: ['United States', 'Canada'],
      },
      {
        id: '3',
        name: 'Fedex International',
        regex: '^[0-9]{15}$',
        trackingSamples: ['123456789012345', '123456789012346'],
        destinationCountries: ['United States', 'Canada'],
      },
    ],
  },
  {
    id: '2',
    carrierId: '2',
    trackingServiceId: '1',
    carriers_on_carrierTrackingService: [
      {
        id: '4',
        name: 'UPS Express',
        regex: '^[0-9]{15}$',
        trackingSamples: ['123456789012345', '123456789012346'],
        destinationCountries: ['United States', 'Canada'],
      },
      {
        id: '5',
        name: 'UPS Ground',
        regex: '^[0-9]{15}$',
        trackingSamples: ['123456789012345', '123456789012346'],
        destinationCountries: ['United States', 'Canada'],
      },
    ],
  },
];
