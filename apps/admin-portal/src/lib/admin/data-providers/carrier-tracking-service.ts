import { DataProvider } from 'react-admin';

import { CARRIER_TRACKING_SERVICES_MOCKED_DATA } from './mocked-data';

export const carrierTrackingServiceDataProvider: DataProvider = {
  getManyReference: async (_, params) => {
    const carrierTrackingServices =
      CARRIER_TRACKING_SERVICES_MOCKED_DATA.filter(
        (carrierTrackingService) =>
          carrierTrackingService.trackingServiceId === params.id
      );

    return {
      data: carrierTrackingServices.flatMap(
        (carrierTrackingService) =>
          carrierTrackingService.carriers_on_carrierTrackingService
      ),
      total: carrierTrackingServices.length,
    };
  },
  getList: async () => {
    throw new Error('Function not implemented.');
  },
  getOne: async () => {
    throw new Error('Function not implemented.');
  },
  getMany: async () => {
    throw new Error('Function not implemented.');
  },
  update: async () => {
    throw new Error('Function not implemented.');
  },
  updateMany: async () => {
    throw new Error('Function not implemented.');
  },
  create: async () => {
    throw new Error('Function not implemented.');
  },
  delete: async () => {
    throw new Error('Function not implemented.');
  },
  deleteMany: async () => {
    throw new Error('Function not implemented.');
  },
};
