import { DataProvider } from 'react-admin';
import { TRACKING_SERVICES_MOCKED_DATA } from './mocked-data';

export const trackingServiceDataProvider: DataProvider = {
  getList: async () => {
    console.log('getList trackingService');
    return {
      data: TRACKING_SERVICES_MOCKED_DATA,
      total: TRACKING_SERVICES_MOCKED_DATA.length,
    };
  },
  getOne: async (_, params) => {
    return {
      data: TRACKING_SERVICES_MOCKED_DATA.find(
        (trackingService) => trackingService.id === params.id
      ),
    };
  },
  getMany: async () => {
    console.log('getMany trackingService');
    return {
      data: TRACKING_SERVICES_MOCKED_DATA,
    };
  },
  getManyReference: async () => {
    console.log('getManyReference trackingService');
    return {
      data: TRACKING_SERVICES_MOCKED_DATA,
      total: TRACKING_SERVICES_MOCKED_DATA.length,
    };
  },
  create: async (_, params) => {
    console.log('create trackingService', params);
    const newTrackingService = {
      id: `${TRACKING_SERVICES_MOCKED_DATA.length + 1}`,
      name: params.data.name,
      apiUrl: params.data.apiUrl,
      apiKey: params.data.apiKey,
    };

    return {
      data: {
        id: newTrackingService.id,
      },
    };
  },
};
