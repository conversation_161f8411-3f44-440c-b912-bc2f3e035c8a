import { Data<PERSON>rovider } from 'react-admin';

import { CARRIER_GROUP_MOCKED_DATA } from './mocked-data';

export const carrierGroupDataProvider: DataProvider = {
  getList: async () => {
    return Promise.resolve({
      data: CARRIER_GROUP_MOCKED_DATA,
      total: CARRIER_GROUP_MOCKED_DATA.length,
    });
  },
  getOne: async (_, params) => {
    return {
      data: CARRIER_GROUP_MOCKED_DATA.find(
        (carrierGroup) => carrierGroup.id === params.id
      ),
    };
  },
  create: async (_, params) => {
    const newCarrierGroup = {
      id: `${CARRIER_GROUP_MOCKED_DATA.length + 1}`,
      name: params.data.name,
      description: params.data.description,
      trackingPageUrl: params.data.trackingPageUrl,
    };

    return {
      data: {
        id: newCarrierGroup.id,
      },
    };
  },
  update: async (_, params) => {
    const updatedCarrierGroup = {
      id: params.id,
      name: params.data.name,
      description: params.data.description,
      trackingPageUrl: params.data.trackingPageUrl,
    };

    return {
      data: updatedCarrierGroup,
    };
  },
};
