import { addDoc, collection, doc, getDoc, updateDoc } from 'firebase/firestore';
import { DataProvider } from 'react-admin';

import { db } from '../firebase';
import { getListFromCollection } from '../operations/get-list';
import { getOneFromCollection } from '../operations/get-one';
import { createUser } from '../operations/create-user';
import { createOrganization } from '../operations/create-organization';

export const organizationDataProvider: DataProvider = {
  getList: (resource, params) => getListFromCollection(db, resource, params),
  getOne: async (resource, params) =>
    getOneFromCollection(db, resource, params),
  getMany: async (resource, params) => {
    const docs = await Promise.all(
      params.ids.map((id) => getDoc(doc(db, resource, id)))
    );
    const data = docs.map((doc) => ({ id: doc.id, ...doc.data() }));
    return { data };
  },
  getManyReference: async (resource, params) => {
    if (params.target === 'organization_id') {
      const path = `organizations/${params.id}/${resource}`;
      const data = await getListFromCollection(db, path, params);
      return data;
    }

    throw new Error('Unknown target');
  },
  getOneReference: async (resource, organization_id, id) => {
    const path = `organizations/${organization_id}/${resource}`;
    const docRef = doc(db, path, id);
    const docSnap = await getDoc(docRef);

    if (!docSnap.exists()) {
      throw new Error('Document not found');
    }

    return {
      data: { id: docSnap.id, ...docSnap.data() },
    };
  },
  create: async (resource, params) => {
    if (resource === 'users') {
      const { organization_id, name, email, password, role } = params.data;
      const result = await createUser(
        organization_id,
        name,
        email,
        password,
        role
      );
      return { data: result };
    }

    if (resource === 'organizations') {
      const { name, settings } = params.data;
      const low_inventory_threshold =
        settings?.products?.low_inventory_threshold;
      const payload: { name: string; low_inventory_threshold?: number } = {
        name,
      };

      if (low_inventory_threshold) {
        payload.low_inventory_threshold = low_inventory_threshold;
      }

      try {
        const result = await createOrganization(payload);
        return {
          data: result,
        };
      } catch (error) {
        console.error('Failed to create organization:', error);
        throw new Error('Failed to create organization');
      }
    }

    let queryResource = resource;

    if (resource === 'locations') {
      queryResource = `organizations/${params.meta.organization_id}/${resource}`;
    }

    const docRef = await addDoc(collection(db, queryResource), params.data);
    return {
      data: { ...params.data, id: docRef.id },
    };
  },
  update: async (resource, params) => {
    let queryResource = resource;

    if (resource === 'locations' || resource === 'users') {
      const { organization_id } = params.meta;
      queryResource = `organizations/${organization_id}/${resource}`;
    }

    const docRef = doc(db, queryResource, params.id);
    await updateDoc(docRef, params.data);

    return {
      data: { ...params.data, id: params.id },
    };
  },
  updateMany: async (resource, params) => {
    await Promise.all(
      params.ids.map((id) => updateDoc(doc(db, resource, id), params.data))
    );
    return { data: params.ids };
  },
  delete: async (resource, params) => {
    throw new Error('Not allowed.');
  },
  deleteMany: async (resource, params) => {
    throw new Error('Not allowed.');
  },
};
