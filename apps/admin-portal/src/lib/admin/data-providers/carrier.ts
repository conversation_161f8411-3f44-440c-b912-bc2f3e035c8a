import { DataProvider } from 'react-admin';
import {
  CARRIER_MOCKED_DATA,
  CARRIER_TRACKING_SERVICES_MOCKED_DATA,
} from './mocked-data';

export const carrierDataProvider: DataProvider = {
  getList: async () => {
    return Promise.resolve({
      data: CARRIER_MOCKED_DATA,
      total: CARRIER_MOCKED_DATA.length,
    });
  },
  getOne: async (_, params) => {
    return {
      data: CARRIER_MOCKED_DATA.find((carrier) => carrier.id === params.id),
    };
  },
  getManyReference: async (_, params) => {
    const carrierTrackingServices =
      CARRIER_TRACKING_SERVICES_MOCKED_DATA.filter(
        (carrierTrackingService) =>
          carrierTrackingService.trackingServiceId === params.id
      );

    return {
      data: carrierTrackingServices.flatMap(
        (carrierTrackingService) =>
          carrierTrackingService.carriers_on_carrierTrackingService
      ),
      total: carrierTrackingServices.length,
    };
  },
  create: async (_, params) => {
    const trackingSamples = params.data.trackingSamples.map(
      ({ trackingSample }) => trackingSample
    );

    const newCarrier = {
      id: `${CARRIER_MOCKED_DATA.length + 1}`,
      name: params.data.name,
      carrierGroupId: params.meta.carrierGroupId,
      regex: params.data.regex,
      trackingSamples,
      destinationCountries: params.data.destinationCountries,
    };

    return {
      data: newCarrier,
    };
  },
};
