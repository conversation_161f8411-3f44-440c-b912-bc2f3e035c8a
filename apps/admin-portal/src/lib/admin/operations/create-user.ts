import { http } from '../http';

export async function createUser(
  organization_id: string,
  name: string,
  email: string,
  password: string,
  role: string
) {
  const url = `organizations/${organization_id}/users`;
  const response = await http.post(url, { name, email, password, role });

  if (response.status !== 201) {
    throw new Error('Failed to create user');
  }

  return response.data;
}
