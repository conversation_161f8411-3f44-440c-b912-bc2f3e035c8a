import {
  collection,
  DocumentData,
  getDocs,
  orderBy,
  Query,
  query,
  QueryConstraint,
} from 'firebase/firestore';

export async function getListFromCollection(db, resource, params) {
  const queryParams: QueryConstraint[] = [];

  if (params.sort && params.sort.field !== 'id') {
    queryParams.push(
      orderBy(params.sort.field, params.sort.order === 'ASC' ? 'asc' : 'desc')
    );
  }

  let col: Query<any, DocumentData>;

  switch (resource) {
    default:
      col = collection(db, resource);
  }

  const querySnapshot = await getDocs(query(col, ...queryParams));

  const data = querySnapshot.docs.map((doc) => ({
    id: doc.id,
    ...doc.data(),
  }));

  // client-side pagination
  if (params.pagination) {
    const { page, perPage } = params.pagination;
    const startIndex = (page - 1) * perPage;
    const endIndex = startIndex + perPage;
    const paginatedData = data.slice(startIndex, endIndex);

    return {
      data: paginatedData,
      total: data.length,
    };
  }

  return {
    data,
    total: data.length,
  };
}
