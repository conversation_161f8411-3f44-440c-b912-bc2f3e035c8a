import { doc, getDoc } from 'firebase/firestore';

export async function getOneFromCollection(db, resource, params) {
  let queryResource = resource;

  if (resource === 'locations' || resource === 'users') {
    const { organization_id } = params.meta;
    queryResource = `organizations/${organization_id}/${resource}`;
  }

  const docRef = doc(db, queryResource, params.id);
  const docSnap = await getDoc(docRef);

  if (!docSnap.exists()) {
    throw new Error('Document not found');
  }

  return {
    data: { id: docSnap.id, ...docSnap.data() },
  };
}
