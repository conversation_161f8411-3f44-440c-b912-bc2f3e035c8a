import { FirebaseApp } from 'firebase/app';
import { connectAuthEmulator, getAuth } from 'firebase/auth';
import { connectFirestoreEmulator, getFirestore } from 'firebase/firestore';

export default function (app: FirebaseApp) {
  if (import.meta.env.DEV) {
    // auth emulator
    if (import.meta.env.VITE_FIREBASE_AUTH_EMULATOR_URL) {
      console.debug(
        'connecting to auth emulator',
        import.meta.env.VITE_FIREBASE_AUTH_EMULATOR_URL
      );
      connectAuthEmulator(
        getAuth(app),
        import.meta.env.VITE_FIREBASE_AUTH_EMULATOR_URL
      );
    }

    // firestore emulator
    if (
      import.meta.env.VITE_FIRESTORE_EMULATOR_HOST &&
      import.meta.env.VITE_FIRESTORE_EMULATOR_PORT
    ) {
      console.debug(
        'connecting to firestore emulator',
        `${import.meta.env.VITE_FIRESTORE_EMULATOR_HOST}:${
          import.meta.env.VITE_FIRESTORE_EMULATOR_PORT
        }`
      );
      connectFirestoreEmulator(
        getFirestore(app),
        import.meta.env.VITE_FIRESTORE_EMULATOR_HOST,
        import.meta.env.VITE_FIRESTORE_EMULATOR_PORT
      );
    }
  }
}
