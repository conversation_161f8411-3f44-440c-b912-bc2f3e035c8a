import axios from 'axios';
import { auth } from './firebase';

export const http = axios.create({
  baseURL: '/api/',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add a request interceptor to include the Firebase Auth token
http.interceptors.request.use(
  async (config) => {
    const access_token = await auth.currentUser?.getIdToken();

    if (!access_token) return Promise.reject('Access token not found');

    config.headers.Authorization = `Bearer ${access_token}`;
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);
