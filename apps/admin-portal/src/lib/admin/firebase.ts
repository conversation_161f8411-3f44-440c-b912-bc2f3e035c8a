import { initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';

// Initialize Firebase
console.assert(import.meta.env.VITE_FIREBASE_CONFIG);
const app = initializeApp(JSON.parse(import.meta.env.VITE_FIREBASE_CONFIG));

// Initialize Firebase Auth and Firestore
const auth = getAuth(app);
auth.tenantId = import.meta.env.VITE_FIREBASE_TENANT_ID;

const db = getFirestore(app);

export { auth, db };
