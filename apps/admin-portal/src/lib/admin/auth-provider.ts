import { signOut } from 'firebase/auth';
import { AuthProvider, UserIdentity } from 'react-admin';

import { auth } from './firebase';

const permissions = {
  admin: [
    {
      resource: 'organizations',
      action: ['list', 'show', 'create', 'edit'],
    },
    {
      resource: 'carriersGroups',
      action: ['list', 'show', 'create', 'edit'],
    },
    {
      resource: 'carriers',
      action: ['list', 'show', 'create', 'edit'],
    },
    {
      resource: 'trackingServices',
      action: ['list', 'show', 'create', 'edit'],
    },
    {
      resource: 'carrierTrackingServices',
      action: ['list', 'show', 'create', 'edit'],
    },
    {
      resource: 'locations',
      action: ['list', 'show', 'create', 'edit'],
    },
    {
      resource: 'users',
      action: ['list', 'show', 'create', 'edit'],
    },
  ],
  member: [
    {
      resource: 'organizations',
      action: ['list', 'show'],
    },
  ],
};

export const authProvider: AuthProvider = {
  login: () => Promise.reject(), // not used
  logout: async () => {
    await signOut(auth);
    localStorage.removeItem('profile');
  },
  checkAuth: async () => {
    await auth.authStateReady();

    if (auth.currentUser) {
      return Promise.resolve();
    }

    return Promise.reject();
  },
  checkError: (error) => {
    if (error.code === 400 || error.code === 403) {
      return Promise.reject();
    }

    return Promise.resolve();
  },
  getIdentity: async () => {
    if (!auth.currentUser) {
      return Promise.reject();
    }

    const identity: UserIdentity = {
      id: auth.currentUser.uid,
    };

    if (auth.currentUser.displayName) {
      identity.fullName = auth.currentUser.displayName;
    }

    if (auth.currentUser.photoURL) {
      identity.avatar = auth.currentUser.photoURL;
    }

    return Promise.resolve(identity);
  },
  canAccess: async (params) => {
    const idToken = await auth.currentUser?.getIdTokenResult();

    if (!idToken?.claims.role) return false;

    const userPermissions = permissions[idToken.claims.role as string];

    if (!userPermissions) return false;

    const resourcePermission = userPermissions.find(
      (permission) => permission.resource === params.resource
    );

    return (
      resourcePermission && resourcePermission.action.includes(params.action)
    );
  },
  getPermissions: async () => {
    const idToken = await auth.currentUser?.getIdTokenResult();

    if (idToken?.claims.role) {
      return permissions[idToken.claims.role as string];
    }

    return [];
  },
};
