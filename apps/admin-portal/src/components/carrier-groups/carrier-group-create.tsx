import { Typography } from '@mui/material';
import { Create, SimpleForm, TextInput, required } from 'react-admin';

export function CarrierGroupCreate() {
  return (
    <Create>
      <SimpleForm sx={{ maxWidth: 600 }}>
        <Typography variant="h6" gutterBottom>
          Create a new carrier group
        </Typography>
        <TextInput
          source="name"
          label="Carrier Group Name"
          validate={required()}
          fullWidth
        />
        <TextInput
          source="description"
          label="Carrier Group Description"
          fullWidth
        />
        <TextInput
          source="trackingPageUrl"
          label="Tracking Page URL"
          fullWidth
        />
      </SimpleForm>
    </Create>
  );
}
