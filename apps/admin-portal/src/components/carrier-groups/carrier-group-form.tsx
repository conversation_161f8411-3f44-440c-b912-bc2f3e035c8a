import {
  ArrayInput,
  AutocompleteInput,
  Datagrid,
  ReferenceInput,
  required,
  AutocompleteArrayInput,
  SimpleFormIterator,
  TextField,
  ArrayField,
  useRecordContext,
} from 'react-admin';

import { CreateInDialogButton } from '@react-admin/ra-form-layout';
import {
  ReferenceManyField,
  SimpleForm,
  TabbedForm,
  TextInput,
} from 'react-admin';
import { DESTINATION_COUNTRY_OPTIONS } from '~/lib/admin/constants';
import { TextChipList } from '../ui/text-chip-list';

export function CarrierGroupForm() {
  const record = useRecordContext();

  return (
    <TabbedForm>
      <TabbedForm.Tab label="General">
        <TextInput source="name" autoFocus />
        <TextInput source="description" />
        <TextInput source="trackingPageUrl" />
      </TabbedForm.Tab>
      <TabbedForm.Tab label="Carriers">
        <ReferenceManyField reference="carriers" target="carrierGroupId">
          <CreateInDialogButton
            fullWidth
            maxWidth="lg"
            mutationOptions={{ meta: { carrierGroupId: record?.id } }}
            label="Create new Carrier"
          >
            <SimpleForm
              display="flex"
              flexDirection="row"
              flexWrap="wrap"
              gap={1}
            >
              <TextInput source="name" validate={required()} fullWidth />
              <AutocompleteArrayInput
                source="destinationCountries"
                choices={DESTINATION_COUNTRY_OPTIONS}
              />
              <TextInput source="regex" validate={required()} fullWidth />
              <ArrayInput source="trackingSamples">
                <SimpleFormIterator disableReordering>
                  <TextInput
                    helperText={false}
                    source="trackingSample"
                    validate={required()}
                  />
                </SimpleFormIterator>
              </ArrayInput>
              <ArrayInput source="carrierTrackingServices_on_carrier">
                <SimpleFormIterator inline disableReordering>
                  <ReferenceInput
                    source="trackingServiceId"
                    reference="trackingServices"
                  >
                    <AutocompleteInput helperText={false} />
                  </ReferenceInput>
                  <TextInput
                    source="carrierCode"
                    validate={required()}
                    fullWidth
                  />
                </SimpleFormIterator>
              </ArrayInput>
            </SimpleForm>
          </CreateInDialogButton>
          <Datagrid rowClick="show">
            <TextField source="name" />
            <TextField source="regex" />
            <ArrayField
              source="carriers_on_carrierGroup"
              label="Tracking Samples"
            >
              <TextChipList source="trackingSamples" />
            </ArrayField>
            <ArrayField
              source="carriers_on_carrierGroup"
              label="Destination Countries"
            >
              <TextChipList source="destinationCountries" />
            </ArrayField>
          </Datagrid>
        </ReferenceManyField>
      </TabbedForm.Tab>
    </TabbedForm>
  );
}
