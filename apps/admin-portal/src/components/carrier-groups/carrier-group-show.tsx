import {
  <PERSON>rray<PERSON>ield,
  <PERSON>grid,
  ReferenceMany<PERSON>ield,
  Show,
  TabbedShowLayout,
  TextField,
} from 'react-admin';
import { TextChipList } from '../ui/text-chip-list';
import { Box } from '@mui/material';

const CustomCarriersEmpty = () => (
  <Box sx={{ padding: 2 }}>
    No carriers are associated with this carrier group
  </Box>
);

export function CarrierGroupShow() {
  return (
    <Show>
      <TabbedShowLayout>
        <TabbedShowLayout.Tab label="Group">
          <TextField source="name" />
          <TextField source="description" />
          <TextField source="trackingPageUrl" />
        </TabbedShowLayout.Tab>
        <TabbedShowLayout.Tab label="Carriers">
          <ReferenceManyField reference="carriers" target="carrierGroupId">
            <Datagrid rowClick="show" empty={<CustomCarriersEmpty />}>
              <TextField source="name" />
              <TextField source="regex" />
              <ArrayField
                source="carriers_on_carrierGroup"
                label="Tracking Samples"
              >
                <TextChipList source="trackingSamples" />
              </ArrayField>
              <ArrayField
                source="carriers_on_carrierGroup"
                label="Destination Countries"
              >
                <TextChipList source="destinationCountries" />
              </ArrayField>
            </Datagrid>
          </ReferenceManyField>
        </TabbedShowLayout.Tab>
      </TabbedShowLayout>
    </Show>
  );
}
