import BusinessIcon from '@mui/icons-material/Business';
import LocalShippingIcon from '@mui/icons-material/LocalShipping';
import TimelineIcon from '@mui/icons-material/Timeline';
import { Admin, Resource } from 'react-admin';

import {
  OrganizationCreate,
  OrganizationEdit,
  OrganizationList,
  OrganizationShow,
} from '~/components/organizations';
import {
  CarrierGroupCreate,
  CarrierGroupShow,
  CarrierGroupEdit,
  CarrierGroupList,
} from '~/components/carrier-groups';
import {
  TrackingServiceCreate,
  TrackingServiceList,
  TrackingServiceShow,
  TrackingServiceEdit,
} from '~/components/tracking-services';
import { authProvider, i18nProvider } from '~/lib/admin';
import { dataProviderCombined } from '~/lib/admin/data-providers';

import { DashboardPage } from './dashboard';
import { SolarLayout } from './layout';
import { LoginPage } from './login';
import { theme } from './theme';

import { getApp } from 'firebase/app';

export default function App() {
  const app = getApp();

  // Temporary check to hide the CMS related resources if not in the dev project
  const isDevProject = app.options.projectId === 'portless-1db76';

  return (
    <Admin
      disableTelemetry
      authProvider={authProvider}
      dataProvider={dataProviderCombined}
      i18nProvider={i18nProvider}
      loginPage={LoginPage}
      requireAuth
      layout={SolarLayout}
      theme={theme}
      dashboard={DashboardPage}
    >
      <Resource
        name="organizations"
        icon={BusinessIcon}
        list={OrganizationList}
        show={OrganizationShow}
        edit={OrganizationEdit}
        create={OrganizationCreate}
        recordRepresentation={(organization) => organization.name}
      />
      {isDevProject ? (
        <>
          <Resource
            name="carriersGroups"
            icon={LocalShippingIcon}
            list={CarrierGroupList}
            show={CarrierGroupShow}
            edit={CarrierGroupEdit}
            create={CarrierGroupCreate}
            recordRepresentation={(carrierGroup) => carrierGroup.name}
          />
          <Resource
            name="trackingServices"
            icon={TimelineIcon}
            list={TrackingServiceList}
            show={TrackingServiceShow}
            edit={TrackingServiceEdit}
            create={TrackingServiceCreate}
            recordRepresentation={(trackingService) => trackingService.name}
          />
        </>
      ) : null}
    </Admin>
  );
}
