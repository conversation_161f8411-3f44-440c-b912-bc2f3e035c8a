import { AppLocationContext, Breadcrumb } from '@react-admin/ra-navigation';
import { Layout, LayoutProps } from 'react-admin';
import { Box, styled } from '@mui/material';

const CustomLayout = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  minHeight: '100vh',
  backgroundColor: theme.palette.background.default,
  '& .RaLayout-appFrame': {
    display: 'flex',
    flexDirection: 'column',
    flexGrow: 1,
  },
  '& .RaLayout-contentWithSidebar': {
    display: 'flex',
    flexGrow: 1,
  },
  '& .RaLayout-content': {
    display: 'flex',
    flexDirection: 'column',
    flexGrow: 2,
    overflow: 'auto',
  },
}));

export const SolarLayout = ({ children, ...props }: LayoutProps) => {
  return (
    <CustomLayout>
      <AppLocationContext>
        <Layout
          {...props}
          sx={{
            '& .RaSidebar-docked': {
              backgroundColor: '#FFF',
            },
            '& .RaAppBar-root': {
              backgroundColor: '#f5f5f5',
              color: '#000000',
              boxShadow: '0 1px 3px rgba(0,0,0,0.12)',
            },
            '& .RaLayout-content': {
              margin: '0 auto',
              maxWidth: '1280px',
              padding: '24px',
            },
          }}
        >
          <Breadcrumb />
          {children}
        </Layout>
      </AppLocationContext>
    </CustomLayout>
  );
};
