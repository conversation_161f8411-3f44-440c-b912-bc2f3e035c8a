import {
  Chip<PERSON>ield,
  <PERSON>grid,
  FunctionField,
  NumberInput,
  PasswordInput,
  ReferenceManyField,
  SelectInput,
  SimpleForm,
  TabbedForm,
  TextField,
  TextInput,
  useEditContext,
  WithRecord,
  required,
  email,
  choices,
} from 'react-admin';
import {
  EditInDialogButton,
  CreateInDialogButton,
} from '@react-admin/ra-form-layout';
import { Box, Typography } from '@mui/material';
import { Location } from '~/types';
import { sanitizeUndefined } from '~/utils/sanitize-undefined';

const validateEmail = [required(), email()];
const validateRole = [required(), choices(['admin', 'member'], 'Invalid role')];

const validateProductTag = (value: string, allValues: Location) => {
  const tagId = allValues?.meta?.mabang?.mabang_product_tag_id;
  if (!value && !tagId) {
    return undefined;
  }
  if (!value && tagId) {
    return 'Product tag name and ID must both be filled or both empty';
  }
  return undefined;
};

const validateProductTagId = (value: string, allValues: Location) => {
  const tagName = allValues?.meta?.mabang?.mabang_product_tag_name;
  if (!value && !tagName) {
    return undefined;
  }
  if (!value && tagName) {
    return 'Product tag name and ID must both be filled or both empty';
  }
  return undefined;
};

export function OrganizationForm() {
  const { record } = useEditContext();
  return (
    <TabbedForm>
      <TabbedForm.Tab label="General">
        <TextInput source="name" fullWidth />
      </TabbedForm.Tab>
      <TabbedForm.Tab label="Locations">
        <ReferenceManyField reference="locations" target="organization_id">
          <CreateInDialogButton
            fullWidth
            maxWidth="sm"
            mutationOptions={{ meta: { organization_id: record?.id } }}
            label="Create new Location"
            transform={(data) => sanitizeUndefined(data)}
          >
            <SimpleForm
              display="flex"
              flexDirection="row"
              flexWrap="wrap"
              gap={1}
            >
              <TextInput source="name" validate={required()} fullWidth />
              <TextInput
                source="address.line_1"
                validate={required()}
                fullWidth
              />
              <TextInput source="address.line_2" fullWidth />
              <TextInput source="address.city" validate={required()} />
              <TextInput source="address.state" validate={required()} />
              <TextInput
                source="address.country_code"
                validate={required()}
                fullWidth
              />
              <Typography variant="h6" sx={{ mt: 2, mb: 1 }}>
                Mabang Configuration
              </Typography>
              <TextInput
                source="meta.mabang.mabang_warehouse_id"
                label="Warehouse ID"
                validate={required()}
                fullWidth
              />
              <TextInput
                source="meta.mabang.mabang_warehouse_name"
                label="Warehouse Name"
                validate={required()}
                fullWidth
              />
              <Box display="flex" gap={2} sx={{ width: '100%' }}>
                <TextInput
                  source="meta.mabang.mabang_product_tag_name"
                  label="Product Tag Name"
                  validate={[validateProductTag]}
                  fullWidth
                />
                <TextInput
                  source="meta.mabang.mabang_product_tag_id"
                  label="Product Tag ID"
                  validate={[validateProductTagId]}
                  fullWidth
                />
              </Box>
              <TextInput
                source="meta.mabang.mabang_label_id"
                label="Label ID"
                fullWidth
              />
            </SimpleForm>
          </CreateInDialogButton>
          <Datagrid size="medium" sx={{ width: '100%' }}>
            <TextField source="name" />
            <FunctionField
              source="address.line_1"
              render={(record) =>
                `${record?.address?.line_1 ?? '-'}, ${
                  record?.address?.line_2 ?? '-'
                }`
              }
            />
            <TextField source="address.city" />
            <TextField source="address.state" />
            <TextField source="address.country_code" />
            <EditInDialogButton
              fullWidth
              maxWidth="sm"
              mutationOptions={{ meta: { organization_id: record?.id } }}
              queryOptions={{ meta: { organization_id: record?.id } }}
              transform={(data) => sanitizeUndefined(data)}
            >
              <SimpleForm>
                <TextInput source="name" validate={required()} />
                <TextInput source="address.line_1" validate={required()} />
                <TextInput source="address.line_2" />
                <TextInput source="address.city" validate={required()} />
                <TextInput source="address.state" validate={required()} />
                <TextInput
                  source="address.country_code"
                  validate={required()}
                />
                <Typography variant="h6" sx={{ mt: 2, mb: 1 }}>
                  Mabang Configuration
                </Typography>
                <TextInput
                  source="meta.mabang.mabang_warehouse_id"
                  label="Warehouse ID"
                  validate={required()}
                  fullWidth
                />
                <TextInput
                  source="meta.mabang.mabang_warehouse_name"
                  label="Warehouse Name"
                  validate={required()}
                  fullWidth
                />
                <Box display="flex" gap={2} sx={{ width: '100%' }}>
                  <TextInput
                    source="meta.mabang.mabang_product_tag_name"
                    label="Product Tag Name"
                    validate={[validateProductTag]}
                    fullWidth
                  />
                  <TextInput
                    source="meta.mabang.mabang_product_tag_id"
                    label="Product Tag ID"
                    validate={[validateProductTagId]}
                    fullWidth
                  />
                </Box>
                <TextInput
                  source="meta.mabang.mabang_label_id"
                  label="Label ID"
                  fullWidth
                />
              </SimpleForm>
            </EditInDialogButton>
          </Datagrid>
        </ReferenceManyField>
      </TabbedForm.Tab>
      <TabbedForm.Tab label="Users">
        <ReferenceManyField reference="users" target="organization_id">
          <WithRecord
            render={(record) => (
              <CreateInDialogButton
                fullWidth
                maxWidth="sm"
                record={{ organization_id: record.id }}
                label="Create new User"
              >
                <SimpleForm>
                  <TextInput source="name" validate={required()} />
                  <TextInput source="email" validate={validateEmail} />
                  <PasswordInput source="password" />
                  <SelectInput
                    source="role"
                    choices={[
                      { id: 'admin', name: 'Admin' },
                      { id: 'member', name: 'Member' },
                    ]}
                    validate={validateRole}
                  />
                </SimpleForm>
              </CreateInDialogButton>
            )}
          />
          <Datagrid size="medium" sx={{ width: '100%' }}>
            <TextField source="name" />
            <TextField source="email" />
            <ChipField source="role" />
            <EditInDialogButton
              fullWidth
              maxWidth="sm"
              mutationOptions={{ meta: { organization_id: record?.id } }}
              queryOptions={{ meta: { organization_id: record?.id } }}
            >
              <SimpleForm>
                <TextInput source="name" validate={required()} />
                <TextInput source="email" readOnly={true} />
                <SelectInput
                  source="role"
                  choices={[
                    { id: 'admin', name: 'Admin' },
                    { id: 'member', name: 'Member' },
                  ]}
                  validate={validateRole}
                />
              </SimpleForm>
            </EditInDialogButton>
          </Datagrid>
        </ReferenceManyField>
      </TabbedForm.Tab>
      <TabbedForm.Tab label="Settings">
        <NumberInput source="settings.products.low_inventory_threshold" />
        <TextInput source="settings.search.token" fullWidth />
      </TabbedForm.Tab>
    </TabbedForm>
  );
}
