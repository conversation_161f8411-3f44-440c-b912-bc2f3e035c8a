import { Typography } from '@mui/material';
import {
  Create,
  NumberInput,
  SimpleForm,
  TextInput,
  required,
} from 'react-admin';

export function OrganizationCreate() {
  return (
    <Create>
      <SimpleForm sx={{ maxWidth: 600 }}>
        <Typography variant="h6" gutterBottom>
          Create a new organization
        </Typography>
        <TextInput
          source="name"
          label="Organization Name"
          validate={required()}
          fullWidth
        />
        <NumberInput
          source="settings.products.low_inventory_threshold"
          fullWidth
        />
      </SimpleForm>
    </Create>
  );
}
