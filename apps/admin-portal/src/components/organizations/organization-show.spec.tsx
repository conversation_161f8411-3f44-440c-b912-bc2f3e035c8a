import { describe, expect, it } from 'vitest';
import { Admin, testDataProvider, Resource } from 'react-admin';
import { fireEvent, render, screen } from '@testing-library/react';
import { i18nProvider } from '~/lib/admin/i18n-provider';
import type { Organization, Location, User } from '~/types';
import { OrganizationShow } from './organization-show';
import { MemoryRouter } from 'react-router';

const dataProvider = {
  getOne: () =>
    Promise.resolve({
      data: {
        id: '1',
        name: 'Organization 1',
        settings: {
          products: { low_inventory_threshold: 10 },
          search: { token: 'token_value' },
        },
      } as Organization,
    }),
  getManyReference: (resource, params) => {
    const refs = {
      locations: [
        {
          id: '1',
          name: 'Location 1',
          address: {
            line_1: '123 Main St',
            line_2: 'Apt 1',
            city: 'City',
            state: 'ST',
            country_code: 'US',
          },
        } as Location,
      ],
      users: [
        {
          id: '1',
          name: 'User 1',
          email: '<EMAIL>',
          role: 'admin',
        } as User,
      ],
    };
    return Promise.resolve({ data: refs[resource], total: 1 });
  },
};

describe('OrganizationShow', () => {
  beforeEach(async () => {
    render(
      <MemoryRouter initialEntries={[{ pathname: '/organizations/1/show' }]}>
        <Admin
          dataProvider={testDataProvider(dataProvider)}
          i18nProvider={i18nProvider}
        >
          <Resource name="organizations" show={OrganizationShow} />
        </Admin>
      </MemoryRouter>
    );
  });

  it('should render tabs general, locations, users and settings', async () => {
    expect(await screen.findByText('General')).toBeInTheDocument();
    expect(await screen.findByText('Locations')).toBeInTheDocument();
    expect(await screen.findByText('Users')).toBeInTheDocument();
    expect(await screen.findByText('Settings')).toBeInTheDocument();
  });

  it('in general tab should render name', async () => {
    const tab = await screen.findByText('General');
    fireEvent.click(tab);

    expect(await screen.findByText('Organization 1')).toBeInTheDocument();
  });

  it('in locations tab should render name, address, city, state and country code', async () => {
    const tab = await screen.findByText('Locations');
    fireEvent.click(tab);

    expect(await screen.findByText('Location 1')).toBeInTheDocument();
  });

  it('in users tab should render name, email and role', async () => {
    const tab = await screen.findByText('Users');
    fireEvent.click(tab);

    expect(await screen.findByText('User 1')).toBeInTheDocument();
    expect(await screen.findByText('<EMAIL>')).toBeInTheDocument();
    expect(await screen.findByText('admin')).toBeInTheDocument();
  });

  it('in settings tab should render low inventory threshold', async () => {
    const tab = await screen.findByText('Settings');
    fireEvent.click(tab);

    expect(
      await screen.findByText('Low inventory threshold')
    ).toBeInTheDocument();
    expect(await screen.findByText('10')).toBeInTheDocument();
  });

  it('in settings tab should render search token', async () => {
    const tab = await screen.findByText('Settings');
    fireEvent.click(tab);

    expect(
      await screen.findByText('Settings search token')
    ).toBeInTheDocument();
  });
});
