import '@testing-library/jest-dom';
import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Admin, DataProvider, Resource, testDataProvider } from 'react-admin';
import { MemoryRouter } from 'react-router';
import { i18nProvider } from '~/lib/admin/i18n-provider';
import { OrganizationCreate } from './organization-create';

const createMock = vi.fn().mockResolvedValue(
  Promise.resolve({
    data: { id: '1', name: 'Test Organization' },
  })
);
const dataProvider: Partial<DataProvider> = {
  create: createMock,
};

describe('OrganizationCreate', () => {
  let rendered: ReturnType<typeof render>;
  beforeEach(async () => {
    rendered = render(
      <MemoryRouter initialEntries={[{ pathname: '/organizations/create' }]}>
        <Admin
          dataProvider={testDataProvider(dataProvider)}
          i18nProvider={i18nProvider}
        >
          <Resource name="organizations" create={OrganizationCreate} />
        </Admin>
      </MemoryRouter>
    );
  });

  it('should renders the create organization form', async () => {
    expect(
      await screen.findByText('Create a new organization')
    ).toBeInTheDocument();
  });

  it('should show validation error when name is not provided', async () => {
    const saveButton = (await screen.findByText('Save')) as HTMLButtonElement;

    expect(saveButton.disabled).toBeTruthy();
  });

  it('should call the dataProvider create method', async () => {
    const nameInput = rendered.container.querySelector(
      '[name="name"]'
    ) as HTMLInputElement;
    fireEvent.change(nameInput, { target: { value: 'Test Organization' } });

    const saveButton = (await screen.findByText('Save')) as HTMLButtonElement;
    expect(saveButton.disabled).toBeFalsy();

    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(createMock).toHaveBeenCalled();
    });
  });
});
