import { describe, expect, it } from 'vitest';
import { Admin, testDataProvider, Resource, DataProvider } from 'react-admin';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { i18nProvider } from '~/lib/admin/i18n-provider';
import type { Organization, Location, User } from '~/types';
import { OrganizationEdit } from './organization-edit';
import { MemoryRouter } from 'react-router';

const db = {
  users: [] as User[],
  locations: [
    {
      id: '1',
      name: 'Location 1',
      address: {
        line_1: '123 Main St',
        line_2: 'Apt 1',
        city: 'City',
        state: 'ST',
        country_code: 'US',
      },
      meta: {
        mabang: {
          mabang_warehouse_id: '1',
          mabang_warehouse_name: 'Warehouse 1',
          mabang_product_tag_name: 'Product Tag 1',
          mabang_product_tag_id: '1',
          mabang_label_id: '1',
        },
      },
    } as Location,
  ],
  organizations: [
    {
      id: '1',
      name: 'Organization 1',
      settings: {
        products: { low_inventory_threshold: 10 },
        search: { token: 'token_value' },
      },
    } as Organization,
  ],
};

const createUserMock = vi.fn((params) => {
  const new_user = {
    ...params.data,
    id: (db.users.length + 1).toString(),
  } as User;
  db.users.push(new_user);
  return Promise.resolve({ data: new_user });
});

const createLocationMock = vi.fn((params) => {
  const new_location = {
    ...params.data,
    id: (db.locations.length + 1).toString(),
  } as Location;
  db.locations.push(new_location);
  return Promise.resolve({ data: new_location });
});

const dataProvider: Partial<DataProvider> = {
  getOne: (resource, params) => {
    const data = db[resource].find((item) => item.id === params.id);
    return Promise.resolve({ data });
  },
  getManyReference: (resource, params) => {
    const data = db[resource];
    return Promise.resolve({ data, total: data.length });
  },
  create: (resource, params) => {
    if (resource === 'users') {
      return createUserMock(params);
    }
    if (resource === 'locations') {
      return createLocationMock(params);
    }
    return Promise.reject('resource not found');
  },
};

describe('OrganizationEdit', () => {
  beforeEach(async () => {
    render(
      <MemoryRouter initialEntries={[{ pathname: '/organizations/1' }]}>
        <Admin
          dataProvider={testDataProvider(dataProvider)}
          i18nProvider={i18nProvider}
        >
          <Resource name="organizations" edit={OrganizationEdit} />
        </Admin>
      </MemoryRouter>
    );
  });

  it('should render tabs general, locations, users and settings', async () => {
    expect(await screen.findByText('General')).toBeInTheDocument();
    expect(await screen.findByText('Locations')).toBeInTheDocument();
    expect(await screen.findByText('Users')).toBeInTheDocument();
    expect(await screen.findByText('Settings')).toBeInTheDocument();
  });

  describe('Create a user', () => {
    beforeEach(async () => {
      const tab = await screen.findByText('Users');
      fireEvent.click(tab);
    });

    it('should see a create user button', async () => {
      expect(await screen.findByText('Create new User')).toBeInTheDocument();
    });

    it('should be able to see a create user model', async () => {
      const createButton = await screen.findByText('Create new User');
      fireEvent.click(createButton);

      const createModelTitle = await screen.findByText('Create User');
      const createModel = createModelTitle.parentElement;

      const nameInput = (await createModel?.querySelector(
        'input[name="name"]'
      )) as HTMLInputElement;
      const emailInput = (await createModel?.querySelector(
        'input[name="email"]'
      )) as HTMLInputElement;
      const passwordInput = (await createModel?.querySelector(
        'input[name="password"]'
      )) as HTMLInputElement;
      const roleInput = (await createModel?.querySelector(
        'input[name="role"]'
      )) as HTMLInputElement;
      const saveButton = (await createModel?.querySelector(
        'button[type="submit"]'
      )) as HTMLButtonElement;

      expect(createModelTitle).toBeInTheDocument();
      expect(nameInput).toBeInTheDocument();
      expect(emailInput).toBeInTheDocument();
      expect(passwordInput).toBeInTheDocument();
      expect(roleInput).toBeInTheDocument();
      expect(saveButton).toBeInTheDocument();
      expect(saveButton.disabled).toBeTruthy();

      fireEvent.change(nameInput, { target: { value: 'User Test' } });
      fireEvent.change(emailInput, {
        target: { value: '<EMAIL>' },
      });
      fireEvent.change(passwordInput, { target: { value: '123456' } });
      fireEvent.change(roleInput, { target: { value: 'admin' } });

      fireEvent.click(saveButton);

      await waitFor(() => expect(createUserMock).toHaveBeenCalled());

      expect(await screen.findByText('User Test')).toBeInTheDocument();
      expect(
        await screen.findByText('<EMAIL>')
      ).toBeInTheDocument();
      expect(await screen.findByText('admin')).toBeInTheDocument();
    });
  });

  describe('Create a location', () => {
    beforeEach(async () => {
      const tab = await screen.findByText('Locations');
      fireEvent.click(tab);
    });

    it('should see a create location button', async () => {
      expect(
        await screen.findByText('Create new Location')
      ).toBeInTheDocument();
    });

    it('should be able to create a new location', async () => {
      const createButton = await screen.findByText('Create new Location');
      fireEvent.click(createButton);

      const createModelTitle = await screen.findByText('Create Location');
      const createModel = createModelTitle.parentElement;

      const nameInput = (await createModel?.querySelector(
        'input[name="name"]'
      )) as HTMLInputElement;
      const addressLine1Input = (await createModel?.querySelector(
        'input[name="address.line_1"]'
      )) as HTMLInputElement;
      const addressLine2Input = (await createModel?.querySelector(
        'input[name="address.line_2"]'
      )) as HTMLInputElement;
      const addressCityInput = (await createModel?.querySelector(
        'input[name="address.city"]'
      )) as HTMLInputElement;
      const addressStateInput = (await createModel?.querySelector(
        'input[name="address.state"]'
      )) as HTMLInputElement;
      const addressCountryCodeInput = (await createModel?.querySelector(
        'input[name="address.country_code"]'
      )) as HTMLInputElement;
      const mabangWarehouseIdInput = (await createModel?.querySelector(
        'input[name="meta.mabang.mabang_warehouse_id"]'
      )) as HTMLInputElement;
      const mabangWarehouseNameInput = (await createModel?.querySelector(
        'input[name="meta.mabang.mabang_warehouse_name"]'
      )) as HTMLInputElement;
      const mabangProductTagNameInput = (await createModel?.querySelector(
        'input[name="meta.mabang.mabang_product_tag_name"]'
      )) as HTMLInputElement;
      const mabangProductTagIdInput = (await createModel?.querySelector(
        'input[name="meta.mabang.mabang_product_tag_id"]'
      )) as HTMLInputElement;
      const mabangLabelIdInput = (await createModel?.querySelector(
        'input[name="meta.mabang.mabang_label_id"]'
      )) as HTMLInputElement;
      const saveButton = (await createModel?.querySelector(
        'button[type="submit"]'
      )) as HTMLButtonElement;

      expect(createModelTitle).toBeInTheDocument();
      expect(nameInput).toBeInTheDocument();
      expect(addressLine1Input).toBeInTheDocument();
      expect(addressLine2Input).toBeInTheDocument();
      expect(addressCityInput).toBeInTheDocument();
      expect(addressStateInput).toBeInTheDocument();
      expect(addressCountryCodeInput).toBeInTheDocument();
      expect(mabangWarehouseIdInput).toBeInTheDocument();
      expect(mabangWarehouseNameInput).toBeInTheDocument();
      expect(mabangProductTagNameInput).toBeInTheDocument();
      expect(mabangProductTagIdInput).toBeInTheDocument();
      expect(mabangLabelIdInput).toBeInTheDocument();
      expect(saveButton).toBeInTheDocument();
      expect(saveButton.disabled).toBeTruthy();

      fireEvent.change(nameInput, { target: { value: 'Location-2' } });
      fireEvent.change(addressLine1Input, {
        target: {
          value: 'Portless, 4th Floor, Area B, Bay Area Digital Warehouse',
        },
      });
      fireEvent.change(addressLine2Input, {
        target: { value: 'Queshan Road 52, Longhua District' },
      });
      fireEvent.change(addressCityInput, { target: { value: 'Shenzhen' } });
      fireEvent.change(addressStateInput, { target: { value: 'Guangdong' } });
      fireEvent.change(addressCountryCodeInput, { target: { value: 'CN' } });
      fireEvent.change(mabangWarehouseIdInput, {
        target: { value: '123456' },
      });
      fireEvent.change(mabangWarehouseNameInput, {
        target: { value: 'Warehouse-2' },
      });
      fireEvent.change(mabangProductTagNameInput, {
        target: { value: 'Product-Tag-2' },
      });
      fireEvent.change(mabangProductTagIdInput, {
        target: { value: '123456' },
      });
      fireEvent.change(mabangLabelIdInput, {
        target: { value: '123456' },
      });

      fireEvent.click(saveButton);

      await waitFor(() => expect(createLocationMock).toHaveBeenCalled());

      expect(await screen.findByText('Location-2')).toBeInTheDocument();
    });
  });
});
