import {
  <PERSON><PERSON>ield,
  <PERSON>grid,
  <PERSON><PERSON><PERSON>ield,
  ReferenceManyField,
  Show,
  TabbedShowLayout,
  TextField,
} from 'react-admin';
import { SensitiveTextField } from '~/components/ui';

export function OrganizationShow() {
  return (
    <Show>
      <TabbedShowLayout>
        <TabbedShowLayout.Tab label="General">
          <TextField source="name" />
        </TabbedShowLayout.Tab>
        <TabbedShowLayout.Tab label="Locations">
          <ReferenceManyField reference="locations" target="organization_id">
            <Datagrid size="medium" sx={{ width: '100%' }}>
              <TextField source="name" />
              <FunctionField
                source="address.line_1"
                render={(record) =>
                  `${record?.address?.line_1 ?? '-'}, ${
                    record?.address?.line_2 ?? '-'
                  }`
                }
              />
              <TextField source="address.city" />
              <TextField source="address.state" />
              <TextField source="address.country_code" />
            </Datagrid>
          </ReferenceManyField>
        </TabbedShowLayout.Tab>
        <TabbedShowLayout.Tab label="Users">
          <ReferenceManyField reference="users" target="organization_id">
            <Datagrid size="medium" sx={{ width: '100%' }}>
              <TextField source="name" />
              <TextField source="email" />
              <ChipField source="role" />
            </Datagrid>
          </ReferenceManyField>
        </TabbedShowLayout.Tab>
        <TabbedShowLayout.Tab label="Settings">
          <TextField source="settings.products.low_inventory_threshold" />
          <SensitiveTextField source="settings.search.token" />
        </TabbedShowLayout.Tab>
      </TabbedShowLayout>
    </Show>
  );
}
