import { render, screen } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import { AdminContext, testDataProvider, Resource } from 'react-admin';
import { BrowserRouter } from 'react-router';
import { type Organization } from '~/types';
import { OrganizationList } from './organization-list';

const dataProvider = {
  getList: () =>
    Promise.resolve({
      data: [
        { id: '1', name: 'Organization 1' },
        { id: '2', name: 'Organization 2' },
      ] as Organization[],
      total: 2,
    }),
};

describe('OrganizationList', () => {
  it('should render the list of organizations', async () => {
    render(
      <AdminContext dataProvider={testDataProvider(dataProvider)}>
        <Resource name="organizations" list={OrganizationList} />
      </AdminContext>,
      { wrapper: BrowserRouter }
    );

    expect(await screen.findByText('Organization 1')).toBeInTheDocument();
    expect(await screen.findByText('Organization 2')).toBeInTheDocument();
  });
});
