import { Box } from '@mui/material';
import {
  <PERSON>grid,
  ReferenceManyField,
  Show,
  TabbedShowLayout,
  TextField,
} from 'react-admin';

const CustomCarriersEmpty = () => (
  <Box sx={{ padding: 2 }}>
    No carriers are associated with this tracking service
  </Box>
);

export function TrackingServiceShow() {
  return (
    <Show>
      <TabbedShowLayout>
        <TabbedShowLayout.Tab label="General">
          <TextField source="name" />
          <TextField source="apiUrl" />
          <TextField source="apiKey" />
        </TabbedShowLayout.Tab>
        <TabbedShowLayout.Tab label="Carriers">
          <ReferenceManyField
            reference="carrierTrackingServices"
            target="trackingServiceId"
          >
            <Datagrid rowClick="show" empty={<CustomCarriersEmpty />}>
              <TextField source="name" />
              <TextField source="regex" />
            </Datagrid>
          </ReferenceManyField>
        </TabbedShowLayout.Tab>
      </TabbedShowLayout>
    </Show>
  );
}
