import { Typography } from '@mui/material';
import { Create, SimpleForm, TextInput, required } from 'react-admin';

export function TrackingServiceCreate() {
  return (
    <Create>
      <SimpleForm sx={{ maxWidth: 600 }}>
        <Typography variant="h6" gutterBottom>
          Create a new tracking service
        </Typography>
        <TextInput
          source="name"
          label="Tracking Service Name"
          validate={required()}
          fullWidth
        />
        <TextInput source="apiUrl" label="API URL" fullWidth />
        <TextInput source="apiKey" label="API Key" fullWidth />
      </SimpleForm>
    </Create>
  );
}
