import { Chip } from '@mui/material';
import { useFieldValue } from 'react-admin';

type TextChipListProps = {
  source: string;
};

export const TextChipList = ({ source }: TextChipListProps) => {
  const array = useFieldValue({ source });

  if (!Array.isArray(array) || array.length === 0) {
    return <div />;
  }

  return (
    <>
      {array.map((item, idx) => (
        <Chip
          label={item}
          style={{ marginRight: '4px' }}
          key={
            typeof item === 'string' || typeof item === 'number' ? item : idx
          }
        />
      ))}
    </>
  );
};
