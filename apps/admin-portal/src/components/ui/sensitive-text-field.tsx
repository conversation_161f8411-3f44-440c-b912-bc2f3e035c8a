import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';
import { useState } from 'react';
import {
  Button,
  type FieldProps,
  FunctionField,
  useFieldValue,
} from 'react-admin';

export const SensitiveTextField = (props: FieldProps) => {
  const [showValue, setShowValue] = useState(false);
  const value = useFieldValue(props);

  const render = () => (showValue ? value : '********');
  const handleVisibilityClick = () => setShowValue(!showValue);
  return (
    <>
      <FunctionField {...props} render={render} />
      <Button onClick={handleVisibilityClick} size="small">
        {showValue ? <VisibilityIcon /> : <VisibilityOffIcon />}
      </Button>
    </>
  );
};
