export type Organization = {
  id: string;
  name: string;
  settings: Settings;
};

export type Settings = {
  products: { low_inventory_threshold: number };
  search: { token: string };
};

export type User = {
  id: string;
  name: string;
  email: string;
  role: string;
};

export type Location = {
  id: string;
  name: string;
  address: {
    line_1: string;
    line_2: string;
    city: string;
    state: string;
    country_code: string;
  };
  meta?: {
    mabang?: {
      mabang_warehouse_id: string;
      mabang_warehouse_name: string;
      mabang_product_tag_name?: string;
      mabang_product_tag_id?: string;
      mabang_label_id?: string;
    };
  };
};
