import type { NextFunction, Request, Response } from 'express';
import { authenticateUser } from '../auth';
import { getAuth, Auth } from 'firebase-admin/auth';
import { deepPartial } from '@portless/utils';

vi.mock('firebase-admin/auth');
vi.mock('../../lib/logger');

describe('authenticateUser middleware', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let nextFunction: NextFunction;
  const mockedGetAuth = vi.mocked(getAuth);
  const mockVerifyIdToken = vi.fn();

  beforeEach(() => {
    mockedGetAuth.mockReturnValueOnce(
      deepPartial<Auth>({
        verifyIdToken: mockVerifyIdToken,
      })
    );

    mockRequest = {
      headers: {},
    };
    mockResponse = {
      status: vi.fn().mockReturnThis(),
      json: vi.fn(),
    };
    nextFunction = vi.fn() as unknown as NextFunction;
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should return 401 when no authorization header is present', async () => {
    await authenticateUser(
      mockRequest as Request,
      mockResponse as Response,
      nextFunction
    );

    expect(mockResponse.status).toHaveBeenCalledWith(401);
    expect(mockResponse.json).toHaveBeenCalledWith({ message: 'Unauthorized' });
    expect(nextFunction).not.toHaveBeenCalled();
  });

  it('should return 401 when authorization header does not start with Bearer', async () => {
    mockRequest.headers = { authorization: 'Basic token123' };

    await authenticateUser(
      mockRequest as Request,
      mockResponse as Response,
      nextFunction
    );

    expect(mockResponse.status).toHaveBeenCalledWith(401);
    expect(mockResponse.json).toHaveBeenCalledWith({ message: 'Unauthorized' });
    expect(nextFunction).not.toHaveBeenCalled();
  });

  it('should return 401 when token verification fails', async () => {
    mockRequest.headers = { authorization: 'Bearer invalid-token' };
    mockVerifyIdToken.mockRejectedValueOnce(new Error('Invalid token'));

    await authenticateUser(
      mockRequest as Request,
      mockResponse as Response,
      nextFunction
    );

    expect(mockVerifyIdToken).toHaveBeenCalledWith('invalid-token');
    expect(mockResponse.status).toHaveBeenCalledWith(401);
    expect(mockResponse.json).toHaveBeenCalledWith({ message: 'Unauthorized' });
    expect(nextFunction).not.toHaveBeenCalled();
  });

  it('should set user in request and call next() when token is valid', async () => {
    const mockDecodedToken = { uid: 'user123', email: '<EMAIL>' };
    mockRequest.headers = { authorization: 'Bearer valid-token' };
    mockVerifyIdToken.mockResolvedValueOnce(mockDecodedToken);

    await authenticateUser(
      mockRequest as Request,
      mockResponse as Response,
      nextFunction
    );

    expect(mockVerifyIdToken).toHaveBeenCalledWith('valid-token');
    expect(mockRequest).toHaveProperty('user', mockDecodedToken);
    expect(nextFunction).toHaveBeenCalled();
    expect(mockResponse.status).not.toHaveBeenCalled();
    expect(mockResponse.json).not.toHaveBeenCalled();
  });
});
