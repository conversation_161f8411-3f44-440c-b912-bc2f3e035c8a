import { Request, Response, NextFunction } from 'express';
import { getAuth } from 'firebase-admin/auth';

import logger from '../lib/logger';

export const authenticateUser = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader?.startsWith('Bearer ')) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    const idToken = authHeader.split('Bearer ')[1];

    if (!idToken) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    const decodedToken = await getAuth().verifyIdToken(idToken);

    // Extend the Request type to include the user
    (req as any).user = decodedToken;

    next();
  } catch (error) {
    logger.error('Authentication error:', error);
    res.status(401).json({ message: 'Unauthorized' });
  }
};
