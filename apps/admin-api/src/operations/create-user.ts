import { Request, Response } from 'express';
import { z } from 'zod';

import logger from '../lib/logger';
import organizationServiceClient from '../lib/organization-client';

export const CreateUserSchema = z.object({
  name: z.string(),
  email: z.string().email(),
  password: z.string().optional(),
  role: z.enum(['admin', 'member']),
});

type CreateUserParams = {
  org_id: string;
};

export async function createUser(
  req: Request<CreateUserParams>,
  res: Response
) {
  logger.info('Create new user for organization', req.params.org_id, req.body);
  const { org_id } = req.params;
  const { name, email, role, password } = req.body;

  const result = await organizationServiceClient.createUser(
    org_id,
    name,
    email,
    role,
    password
  );

  logger.debug('result', result);

  logger.info('Created org', result.data?.id);
  res.status(201).json(result.data);
}
