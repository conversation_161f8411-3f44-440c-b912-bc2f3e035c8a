import { Request, Response } from 'express';

import logger from '../lib/logger';
import organizationServiceClient from '../lib/organization-client';

type CreateOrganizationParams = {
  name: string;
  low_inventory_threshold?: number;
};

export async function createOrganization(
  req: Request<CreateOrganizationParams>,
  res: Response
) {
  logger.info('Create new organization', req.body);

  const result = await organizationServiceClient.createOrganization(req.body);

  logger.debug('result', result);

  logger.info('Created org', result.data);
  res.status(201).json(result.data);
}
