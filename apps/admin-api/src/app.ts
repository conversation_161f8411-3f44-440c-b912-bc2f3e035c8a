import express from 'express';
import * as OpenApiValidator from 'express-openapi-validator';
import morgan from 'morgan';

import logger from './lib/logger';
import { authenticateUser } from './middlewares/auth';
import organizations from './routes/organizations';

const app = express();

app.use(
  morgan('tiny', {
    stream: {
      write: (message: string) => logger.info(message),
    },
  })
);

// body parsers - important in this order
app.use(express.json());

app.use(
  OpenApiValidator.middleware({
    apiSpec: './openapi.yaml',
    validateRequests: true,
    validateResponses: true,
  })
);

// Add authentication middleware after body parsers but before routes
app.use(authenticateUser);

// register api routes
app.use('/api', organizations);

// error handler
app.use((err, _req, res, _next) => {
  logger.error(err);
  res
    .status(err.status || 500)
    .json({ message: err.message, error: err.errors });
});

export default app;
