import request from 'supertest';
import organizationServiceClient from '../lib/organization-client';
import app from '../app';
import { getAuth, Auth } from 'firebase-admin/auth';
import { deepPartial } from '@portless/utils';

vi.mock('firebase-admin/auth');
vi.mock('../lib/organization-client.ts');
vi.mock('../lib/logger.ts');
vi.mock('express-openapi-validator', () => ({
  middleware: () => (req, res, next) => next(),
}));

describe('App tests', () => {
  const mockedOrganizationServiceClient = vi.mocked(organizationServiceClient);
  const mockedGetAuth = vi.mocked(getAuth);

  beforeEach(() => {
    mockedGetAuth.mockReturnValue(
      deepPartial<Auth>({
        verifyIdToken: vi.fn().mockResolvedValueOnce({
          uid: '12345',
          email: '<EMAIL>',
        }),
      })
    );
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Organization routes', () => {
    it('POST /organizations', async () => {
      const body = { name: 'Test Organization' };
      const resultBody = { id: '12345', ...body };
      mockedOrganizationServiceClient.createOrganization = vi
        .fn()
        .mockResolvedValue({
          data: resultBody,
        });

      const response = await request(app)
        .post('/api/organizations')
        .set('Content-type', 'application/json')
        .set('Authorization', 'Bearer valid-token')
        .send(body);

      expect(
        mockedOrganizationServiceClient.createOrganization
      ).toHaveBeenCalledWith(body);
      expect(response.status).toBe(201);
      expect(response.body).toEqual(resultBody);
    });

    it('should return 401 when no token is provided', async () => {
      const response = await request(app)
        .post('/api/organizations')
        .send({ name: 'Test Organization' });

      expect(response.status).toBe(401);
    });
  });

  describe('User routes', () => {
    it('POST /organizations/:org_id/users', async () => {
      const org_id = '1';
      const body = {
        name: 'Test User',
        email: '<EMAIL>',
        password: 'test_password',
        role: 'admin',
      };
      const resultBody = { id: '12345', ...body };

      mockedOrganizationServiceClient.createUser = vi.fn().mockResolvedValue({
        data: resultBody,
      });

      const response = await request(app)
        .post(`/api/organizations/${org_id}/users`)
        .set('Authorization', 'Bearer valid-token')
        .send(body);

      expect(mockedOrganizationServiceClient.createUser).toHaveBeenCalledWith(
        org_id,
        body.name,
        body.email,
        body.role,
        body.password
      );
      expect(response.status).toBe(201);
      expect(response.body).toEqual(resultBody);
    });

    it('should return 401 when no token is provided', async () => {
      const response = await request(app)
        .post('/api/organizations')
        .send({ name: 'Test Organization' });

      expect(response.status).toBe(401);
    });
  });
});
