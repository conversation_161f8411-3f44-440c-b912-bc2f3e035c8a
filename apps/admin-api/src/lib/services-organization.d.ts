/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export interface paths {
  '/organizations/create': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /** Create a new organization */
    post: {
      parameters: {
        query?: never;
        header?: never;
        path?: never;
        cookie?: never;
      };
      requestBody?: {
        content: {
          'application/json': {
            name: string;
            low_inventory_threshold?: number;
          };
        };
      };
      responses: {
        /** @description Organization created */
        201: {
          headers: {
            [name: string]: unknown;
          };
          content: {
            'application/json': {
              id: string;
              name?: string;
              low_inventory_threshold?: number;
            };
          };
        };
        400: components['responses']['400-BadRequest'];
        401: components['responses']['401-Unauthorized'];
      };
    };
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/organizations/{org_id}/users': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /** Create a new user inside an organization */
    post: {
      parameters: {
        query?: never;
        header?: never;
        path: {
          /** @description ID of organization */
          org_id: string;
        };
        cookie?: never;
      };
      requestBody?: {
        content: {
          'application/json': {
            name: string;
            email: string;
            password?: string;
            role: string;
          };
        };
      };
      responses: {
        /** @description User created */
        201: {
          headers: {
            [name: string]: unknown;
          };
          content: {
            'application/json': {
              id: string;
              name: string;
              email: string;
              role: string;
            };
          };
        };
        400: components['responses']['400-BadRequest'];
        401: components['responses']['401-Unauthorized'];
      };
    };
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
}
export type webhooks = Record<string, never>;
export interface components {
  schemas: never;
  responses: {
    /** @description Bad request */
    '400-BadRequest': {
      headers: {
        [name: string]: unknown;
      };
      content: {
        'application/json': {
          message: string;
          error?: string[];
        };
      };
    };
    /** @description Unauthorized */
    '401-Unauthorized': {
      headers: {
        [name: string]: unknown;
      };
      content: {
        'application/json': {
          message: string;
          error?: string[];
        };
      };
    };
  };
  parameters: never;
  requestBodies: never;
  headers: never;
  pathItems: never;
}
export type $defs = Record<string, never>;
export type operations = Record<string, never>;
