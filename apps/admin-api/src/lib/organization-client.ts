import createClient from 'openapi-fetch';
import { defineString } from 'firebase-functions/params';
import { paths } from './services-organization';

const baseUrl = defineString('PORTLESS_ORGANIZATION_SERVICE_URL');

const client = createClient<paths>({
  baseUrl: baseUrl.value(),
});

export default {
  createOrganization: (payload: {
    name: string;
    low_inventory_threshold?: number;
  }) =>
    client.POST('/organizations/create', {
      body: payload,
    }),
  createUser: (
    org_id: string,
    name: string,
    email: string,
    role: string,
    password?: string
  ) => {
    const body = !password
      ? { name, email, role }
      : { name, email, role, password };

    return client.POST('/organizations/{org_id}/users', {
      params: {
        path: { org_id },
      },
      body,
    });
  },
};
