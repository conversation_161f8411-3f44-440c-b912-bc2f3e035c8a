# apps-admin-api

This Nx Firebase function was generated by [@simondotm/nx-firebase](https://github.com/simondotm/nx-firebase).

## Generated Application Files

- `src/main.ts` - Default Firebase Functions entry point

## Generated Workspace Root Files

- `firebase.json` - Firebase CLI Configuration for this project
- `.firebaserc` - Default Firebase CLI Deployment Targets Configuration
- `firebase.json` - Intentionally Empty Firebase CLI Configuration (only needed to allow Firebase CLI to run in your workspace)

## Generated modules

Nx-Firebase will add `firebase-admin` and `firebase-functions` to your workspace `package.json` at the `'latest'` version. You may wish to set these to a specific version.

## Next Steps

- `npm install -g firebase-tools` - Install the [Firebase CLI](https://firebase.google.com/docs/cli)
- `firebase login` - Authenticate the Firebase CLI
- `firebase use --add` - Add your Firebase Project as a target to `.firebaserc`
- You do not need to `npm install` in the app project directory, but can still add and run custom npm scripts to the app `package.json` if you wish

See the plugin [README](https://github.com/simondotm/nx-firebase/blob/main/README.md) for more information.
