{"name": "apps-admin-api", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/admin-api/src", "projectType": "application", "tags": ["firebase:function", "firebase:name:apps-admin-api", "firebase:dep:projects-portless", "test-suite:unit"], "implicitDependencies": ["services-organization"], "targets": {"build": {"executor": "@nx/esbuild:esbuild", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/admin-api", "main": "apps/admin-api/src/main.ts", "tsConfig": "apps/admin-api/tsconfig.app.json", "assets": ["apps/admin-api/src/assets", {"glob": "openapi.yaml", "input": "apps/admin-api", "output": "."}, {"glob": "**/*", "input": "apps/projects/portless/environment", "output": "."}], "generatePackageJson": true, "platform": "node", "bundle": true, "thirdParty": false, "dependenciesFieldType": "dependencies", "target": "node22", "format": ["esm"], "esbuildOptions": {"logLevel": "info"}}}, "test": {"executor": "@nx/vite:test", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"]}, "deploy": {"executor": "nx:run-commands", "options": {"command": "nx run projects-portless:deploy --only functions:apps-admin-api"}, "dependsOn": ["build"]}, "lint": {"executor": "@nx/eslint:lint"}}}