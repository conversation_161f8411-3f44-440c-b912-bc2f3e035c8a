openapi: 3.1.0
info:
  title: Admin API
  version: 1.0.0
paths:
  /api/organizations:
    post:
      summary: Create a new organization
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                low_inventory_threshold:
                  type: number
              required:
                - name
      responses:
        '201':
          description: Organization created
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                  name:
                    type: string
                  low_inventory_threshold:
                    type: number
                required:
                  - id
        '400':
          $ref: '#/components/responses/400-BadRequest'
        '401':
          $ref: '#/components/responses/401-Unauthorized'

  /api/organizations/{org_id}/users:
    post:
      summary: Create a new user inside an organization
      parameters:
        - name: org_id
          in: path
          description: ID of organization
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                email:
                  type: string
                password:
                  type: string
                role:
                  type: string
              required:
                - name
                - email
                - role
      responses:
        '201':
          description: User created
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                  name:
                    type: string
                  email:
                    type: string
                  role:
                    type: string
                required:
                  - id
                  - name
                  - email
                  - role
        '400':
          $ref: '#/components/responses/400-BadRequest'
        '401':
          $ref: '#/components/responses/401-Unauthorized'

components:
  responses:
    400-BadRequest:
      description: Bad request
      content:
        application/json:
          schema:
            type: object
            properties:
              message:
                type: string
              error:
                type: array
                items:
                  type: string
            required:
              - message
    401-Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            type: object
            properties:
              message:
                type: string
              error:
                type: array
                items:
                  type: string
            required:
              - message
