# See http://help.github.com/ignore-files/ for more about ignoring files.

# compiled output
dist
tmp
/out-tsc

# dependencies
node_modules

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# misc
/.sass-cache
/connect.lock
/coverage
/coverage-main
/libpeerconnection.log
npm-debug.log
yarn-error.log
testem.log
/typings

# System Files
.DS_Store
Thumbs.db

.nx/cache
.nx/workspace-data
# Nx-Firebase
.runtimeconfig.json
**/.emulators/*
**/.firebase/*
database-debug.log
firebase-debug.log
firestore-debug.log
pubsub-debug.log
ui-debug.log
firebase-export*
.secret.local

.env.local
.env

vite.config.*.timestamp*
vitest.config.*.timestamp*
.cursor/rules/nx-rules.mdc
.github/instructions/nx.instructions.md
