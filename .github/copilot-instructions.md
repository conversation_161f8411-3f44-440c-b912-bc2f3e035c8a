# TypeScript Style Guide

This document outlines the coding style conventions for TypeScript code within this project.

## Naming Conventions

- **Interfaces:** Use PascalCase (e.g., `IBaseExporter`). Consider prefixing with `I` if it clarifies the intent, but PascalCase is the minimum requirement.
- **Classes:** Use PascalCase (e.g., `ProductExporter`).
- **Types (Aliases/Inferred):** Use PascalCase (e.g., `Product`, `ProductInventory`).
- **Constants:** Use SCREAMING_SNAKE_CASE for top-level or exported constants (e.g., `PRODUCTS_CHUNK_LENGTH`). Use camelCase for local constants within functions if preferred.
- **Variables & Functions:** Use camelCase (e.g., `batchIteration`, `transformAndSaveProducts`).
- **Object Properties (Data Models/Schemas):** Prefer snake_case for properties within data models, entities, or schemas, especially when interacting with databases or external APIs that use this convention (e.g., `organization_id`, `updated_at`, `ext_id`).
- **Private Members:** Use the `private` keyword. Do not use underscore prefixes (e.g., `private readonly storage`).

## Code Structure & Formatting

- **Indentation:** Use 2 spaces for indentation. Do not use tabs.
- **Quotes:** Use single quotes (`'`) for all string literals. Use template literals (`` ` ``) for strings requiring interpolation or spanning multiple lines.
- **Semicolons:** Use semicolons at the end of statements.
- **Imports:**
  - Use named imports (`import { X, Y } from 'module';`). Avoid default imports where named imports are available.
  - Group imports logically: Node built-ins, external packages, internal monorepo packages (`@portless/...`), local modules (`./`, `../`). Separate groups with a blank line.
  - Order imports alphabetically within each group.
- **Classes:**
  - Declare classes using the `class` keyword.
  - Use the `constructor` for initializing class properties and handling dependency injection.
  - Mark constructor parameters for injected dependencies as `private readonly`.
  - Prefer arrow functions (`myMethod = () => {}`) for class methods, especially when they are used as callbacks or event handlers, to automatically bind `this`.
- **Functions:** Use the `function` keyword for top-level functions or `const myFunction = () => {}` for function expressions.
- **Spacing:** Use standard spacing around operators (`=`, `+`, `-`, `*`, `/`, `=>`, etc.) and after commas.

## TypeScript Specifics

- **Types:**
  - Use explicit types for function parameters, return values, and variable declarations where the type is not immediately obvious from the assigned value.
  - Prefer `interface` for defining object shapes that might be implemented by classes. Use `type` for aliases, unions, intersections, and more complex types.
  - When using Zod for schema definition (e.g., `ProductSchema`), export the inferred type as well (e.g., `export type Product = z.infer<typeof ProductSchema>;`).
- **Access Modifiers:** Use `public`, `private`, `protected` explicitly. Default is `public`. Use `readonly` for properties that should not be reassigned after initialization (especially constructor parameters).
- **Async/Await:** Use `async`/`await` for handling Promises and asynchronous operations. Avoid mixing `.then()`/`.catch()` with `async`/`await` unless necessary for specific patterns.
- **Null/Undefined:** Be explicit about `null` and `undefined`. Use Zod's `.optional()`, `.nullable()`, or `.nullish()` where appropriate in schemas. Use strict null checks (`compilerOptions.strictNullChecks = true` in `tsconfig.json`).

## Data Modeling (Zod)

- Define reusable schemas for core entities (e.g., `ProductSchema`, `ProductInventorySchema`).
- Use descriptive names for schemas (PascalCase).
- Leverage Zod methods like `.object()`, `.string()`, `.number()`, `.array()`, `.optional()`, `.nullable()`, `.enum()` etc., for precise definitions.

## Error Handling

- Use `try...catch` blocks for synchronous code that might throw errors and within `async` functions to handle awaited promise rejections.
- For Promise-based operations without `async/await` (like stream event handlers), use `.catch()` or the `'error'` event handler.
- Log errors using the standard logger (`firebase-functions/logger`) with appropriate levels (`error`, `warn`). Include contextual information in logs.

## Logging

- Use the `firebase-functions/logger` module (`debug`, `info`, `warn`, `error`).
- Provide meaningful log messages.
- Include relevant context (e.g., IDs, SKUs, batch numbers) as structured data in the second argument to the logger functions for better analysis.

## Testing (Vitest)

- **Framework:** Use Vitest (`vitest`) as the testing framework.
- **File Naming:** Test files should be named using the pattern `*.test.ts` or `*.spec.ts` and located alongside the code they test or in a dedicated `__tests__` directory.
- **Structure:**
  - Use `describe` blocks to group related tests, typically one per function or class.
  - Use `test` or `it` for individual test cases. Test case names should clearly describe what is being tested.
  - Use `beforeEach` for setup common to multiple tests within a `describe` block (e.g., resetting mocks).
  - Use `afterEach` for cleanup common to multiple tests (e.g., `vi.resetAllMocks()`).
- **Mocking:**
  - Use `vi.mock('module/path')` to mock modules or dependencies.
  - Use `vi.fn()` to create mock functions.
  - Use `vi.mocked()` to get typed access to mocked modules/functions.
  - Reset mocks in `afterEach(() => { vi.resetAllMocks(); });` to ensure test isolation.
- **Assertions:** Use `expect` with Vitest's built-in matchers (e.g., `expect(...).toBe(...)`, `expect(...).toHaveBeenCalledWith(...)`, `expect(...).rejects.toThrow(...)`).
- **Test Cases:**
  - Aim for clear, focused test cases that test one specific aspect or scenario.
  - Use `test.each` for parameterized tests to reduce boilerplate when testing the same logic with different inputs/outputs.
- **Asynchronous Code:** Use `async/await` when testing asynchronous functions. Use `expect(...).resolves` and `expect(...).rejects` for testing promises.
