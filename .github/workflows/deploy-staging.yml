name: 'From main branch @ Push'

on:
  push:
    branches: [main]

permissions:
  actions: read
  contents: read

jobs:
  autoupdate:
    name: autoupdate
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write
      actions: read
    steps:
      - uses: docker://chinthakagodawita/autoupdate-action:v1
        env:
          GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN }}'
          PR_FILTER: 'labelled'
          PR_LABELS: 'autoupdate'
          MERGE_CONFLICT_ACTION: 'ignore'
  deploy_staging:
    name: Build and Deploy to Firebase Staging
    runs-on: ubuntu-latest
    environment:
      name: staging
      url: https://console.firebase.google.com/project/portless-bd9eb/overview

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: 'npm'

      - name: Install NPM 11
        run: npm install -g npm@11

      - run: npm ci

      - name: Detect changed projects
        uses: nrwl/nx-set-shas@v4

      - name: Set Up Firebase
        run: npm install -g firebase-tools

      - name: Authenticate to Firebase
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY_STAGING_JSON }}

      - name: Use staging as environment
        run: npx nx run projects-portless:firebase use staging

      - name: Deploy affected services
        timeout-minutes: 20
        uses: mansagroup/nrwl-nx-action@v3
        with:
          targets: deploy
          affected: 'true'
          nxCloud: false

  testunit:
    name: Unit test Coverage
    runs-on: ubuntu-latest
    environment: testing
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: 'npm'

      - name: Install NPM 11
        run: npm install -g npm@11

      - run: npm ci

      - name: Detect changed projects
        uses: nrwl/nx-set-shas@v4

      - name: Unit test with Coverage
        uses: mansagroup/nrwl-nx-action@v3
        with:
          targets: test
          affected: 'true'
          nxCloud: false
          parallel: '10'
          args: "--passWithNoTests --coverage --exclude='*,!tag:test-suite:unit'"

      - name: Upload coverage reports to Codecov
        uses: codecov/codecov-action@v5.4.3
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          directory: ./coverage

  coverage:
    name: Coverage from main
    runs-on: ubuntu-latest
    environment: testing
    env:
      ZONOS_API_KEY: ${{ secrets.ZONOS_API_KEY }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: 'npm'

      - name: Install NPM 11
        run: npm install -g npm@11

      - run: npm ci

      - name: Test with firebase emulators
        uses: w9jds/firebase-action@v14.0.1
        with:
          args: emulators:exec --only auth,firestore,functions,pubsub \"npm run test\"
        env:
          GCP_SA_KEY: ${{ secrets.GCP_SA_KEY_JSON }}

      - name: Upload coverage reports to Codecov
        uses: codecov/codecov-action@v5.4.3
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          directory: ./coverage
