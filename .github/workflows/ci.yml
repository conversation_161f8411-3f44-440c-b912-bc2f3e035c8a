name: CI

on:
  pull_request:
    branches: [main]

permissions:
  actions: read
  contents: read
  pull-requests: write

# Automatically cancel in-progress actions on the same branch
concurrency:
  group: ${{ github.workflow }}-${{ github.event_name == 'pull_request_target' && github.head_ref || github.ref }}
  cancel-in-progress: true

env:
  VITE_FIREBASE_CONFIG: ${{ secrets.GCP_SA_KEY_STAGING_JSON }}
  ZONOS_API_KEY: ${{ secrets.ZONOS_API_KEY }}

jobs:
  auto-assign:
    name: Auto assign
    runs-on: ubuntu-latest
    steps:
      - uses: kentaro-m/auto-assign-action@v2.0.0

  lint:
    name: Lint affected projects
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      # Connect your workspace on nx.app and uncomment this to enable task distribution.
      # The "--stop-agents-after" is optional, but allows idle agents to shut down once the "e2e-ci" targets have been requested
      # - run: npx nx-cloud start-ci-run --distribute-on="5 linux-medium-js" --stop-agents-after="e2e-ci"

      # Cache node_modules
      - uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: 'npm'

      - name: Install NPM 11
        run: npm install -g npm@11

      - run: npm ci

      - uses: nrwl/nx-set-shas@v4

      - run: chmod +x pretty-format-check.sh

      - name: Lint affected projects
        uses: mansagroup/nrwl-nx-action@v3
        with:
          targets: lint
          affected: 'true'
          nxCloud: false

  build:
    name: Build all projects
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: 'npm'

      - name: Install NPM 11
        run: npm install -g npm@11

      - run: npm ci

      - name: Build all projects
        uses: mansagroup/nrwl-nx-action@v3
        with:
          targets: build
          all: 'true'
          parallel: '10'
          nxCloud: false

  testunit:
    name: Unit test Coverage
    runs-on: ubuntu-latest
    environment: testing
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: 'npm'

      - name: Install NPM 11
        run: npm install -g npm@11

      - run: npm ci

      - name: Detect changed projects
        uses: nrwl/nx-set-shas@v4

      - name: Unit test with Coverage
        uses: mansagroup/nrwl-nx-action@v3
        with:
          targets: test
          affected: 'true'
          nxCloud: false
          parallel: '10'
          args: "--passWithNoTests --coverage --exclude='*,!tag:test-suite:unit'"

      - name: Upload coverage reports to Codecov
        uses: codecov/codecov-action@v5.4.3
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          directory: ./coverage

  test:
    name: Feature test Coverage with Firebase Emulators
    runs-on: ubuntu-latest
    environment: testing
    timeout-minutes: 10

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: 'npm'

      - name: Install NPM 11
        run: npm install -g npm@11

      - run: npm ci

      - name: Detect changed projects
        uses: nrwl/nx-set-shas@v4

      - name: Build all projects
        uses: mansagroup/nrwl-nx-action@v3
        with:
          targets: build
          all: 'true'
          parallel: '10'
          nxCloud: false

      - name: Test with firebase emulators
        uses: w9jds/firebase-action@v14.0.1
        with:
          args: emulators:exec --only auth,firestore,functions,pubsub \"npm run test:pr\"
        env:
          GCP_SA_KEY: ${{ secrets.GCP_SA_KEY_JSON }}

      - name: Upload coverage reports to Codecov
        uses: codecov/codecov-action@v5.4.3
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          directory: ./coverage
