name: 'Production: Deploy Selected Services'

on:
  workflow_dispatch:
    inputs:
      services:
        description: 'Services to deploy'
        required: true
        type: choice
        options:
          - services-mabang
          - services-oms
          - services-17track
          - services-aftership
          - services-tms
          - services-portless
          - services-wms
          - services-zonos
          - services-organization

permissions:
  actions: read
  contents: read

jobs:
  deploy_selected_services:
    name: Deploy Selected Services to Firebase Production
    runs-on: ubuntu-latest
    environment:
      name: production
      url: https://console.firebase.google.com/project/portless-production/overview

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: 'npm'

      - name: Install NPM 11
        run: npm install -g npm@11

      - run: npm ci

      - name: Set Up Firebase
        run: npm install -g firebase-tools

      - name: Authenticate to Firebase
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY_PROD_JSON }}

      - name: Use production as environment
        run: npx nx run projects-portless:firebase use production

      - name: Set service to deploy
        id: set-service
        run: |
          SELECTED_SERVICES="${{ github.event.inputs.services }}"
          echo "Will deploy specific service: $SELECTED_SERVICES"
          echo "service_filter=projects=$SELECTED_SERVICES" >> $GITHUB_OUTPUT

      - name: Deploy services
        timeout-minutes: 20
        uses: mansagroup/nrwl-nx-action@v3
        with:
          targets: deploy
          projects: ${{ github.event.inputs.services }}
          nxCloud: false

      - name: Notify deployment completion
        if: success()
        run: |
          echo "✅ Successfully deployed ${{ github.event.inputs.services }} to production"
