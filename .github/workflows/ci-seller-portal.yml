name: <PERSON><PERSON> Seller Portal

on:
  pull_request:
    branches: [main]
    paths:
      - 'apps/seller-portal/**'
      - 'apps/seller-portal-api/**'

concurrency:
  group: ${{ github.workflow }}-${{ github.event_name == 'pull_request_target' && github.head_ref || github.ref }}
  cancel-in-progress: true

permissions:
  actions: read
  contents: read
  pull-requests: write

env:
  GITHUB_PR_NUMBER: ${{ github.event.pull_request.number }}

jobs:
  seller-portal-ci:
    name: 'Build and deploy live preview'
    runs-on: ubuntu-latest
    environment:
      name: preview
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: 'npm'

      - name: Install NPM 11
        run: npm install -g npm@11

      - run: npm ci

      - name: Set Up Firebase
        run: npm install -g firebase-tools

      - name: Authenticate to Firebase
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY_DEVELOPMENT_JSON }}

      - name: Use development as environment
        run: npx firebase use development

      - name: Build seller portal
        uses: mansagroup/nrwl-nx-action@v3
        with:
          targets: build
          projects: seller-portal,apps-seller-portal-api
          parallel: '10'
          nxCloud: false
          args: '--configuration development'

      # This works, but I need to update IAM
      - name: Deploy seller portal to preview channel
        id: deploy-preview
        timeout-minutes: 10
        run: |
          output=$(npx firebase hosting:channel:deploy pull-request-${{ github.event.pull_request.number }} --only seller-portal --expires 7d 2>&1)
          echo "$output"

          # Extract the preview URL from the output
          preview_url=$(echo "$output" | grep -oE 'https://[^[:space:]]+--pull-request-[0-9]+-[^[:space:]]+\.web\.app')
          echo "preview_url=$preview_url" >> $GITHUB_OUTPUT

          if [ -n "$preview_url" ]; then
            echo "Preview URL found: $preview_url"
          else
            echo "Warning: Could not extract preview URL from deployment output"
            exit 1
          fi

      - name: Comment preview URL on PR
        if: steps.deploy-preview.outputs.preview_url != ''
        uses: actions/github-script@v7
        with:
          script: |
            const previewUrl = '${{ steps.deploy-preview.outputs.preview_url }}';
            const prNumber = ${{ github.event.pull_request.number }};

            const commentBody = `## 🚀 Seller Portal Preview Deployed

            Your changes have been deployed to a preview environment:

            **Preview URL:** ${previewUrl}

            This preview will expire in 7 days.

            <sub>Deployed from commit: \`${{ github.sha }}\`</sub>`;

            // Check if we already have a comment from this workflow
            const { data: comments } = await github.rest.issues.listComments({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: prNumber
            });

            const existingComment = comments.find(comment =>
              comment.body.includes('🚀 Seller Portal Preview Deployed') &&
              comment.user.login === 'github-actions[bot]'
            );

            if (existingComment) {
              // Update existing comment
              await github.rest.issues.updateComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                comment_id: existingComment.id,
                body: commentBody
              });
            } else {
              // Create new comment
              await github.rest.issues.createComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: prNumber,
                body: commentBody
              });
            }
