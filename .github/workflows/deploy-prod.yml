name: 'From main branch @ Release'

on:
  release:
    types: [published]

permissions:
  actions: read
  contents: read

jobs:
  deploy_production:
    if: github.event.release.prerelease == false
    name: Build and Deploy to Firebase Production
    runs-on: ubuntu-latest
    environment:
      name: production
      url: https://console.firebase.google.com/project/portless-production/overview

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: 'npm'

      - run: npm ci

      - name: Detect changes from latest release
        id: previous_release
        uses: actions/github-script@v7
        with:
          script: |
            // Fetch the latest 10 releases (might include drafts and prereleases)
            const response = await github.rest.repos.listReleases({
              owner: context.repo.owner,
              repo: context.repo.repo,
              per_page: 10
            });
            const releases = response.data.filter(r => !r.draft && !r.prerelease);

            // Ensure there are at least two releases
            if (releases.length < 2) {
              core.setFailed("Not enough releases to perform a comparison.");
            }

            // Assuming the first release is the latest (which triggered the event),
            // find the next one in the array.
            const currentReleaseId = context.payload.release.id;
            const previousRelease = releases.find(r => r.id !== currentReleaseId);

            if (!previousRelease) {
              core.setFailed("No previous release found.");
            }

            core.setOutput("prev_release_tag", previousRelease.tag_name);
          result-encoding: string

      - name: Log previous release tag
        run: |
          echo "Previous release tag: ${{ steps.previous_release.outputs.prev_release_tag }}"

      - name: Set NX_BASE and NX_HEAD environment variables
        run: |
          echo "NX_BASE=${{ steps.previous_release.outputs.prev_release_tag }}" >> $GITHUB_ENV
          # For example, setting NX_HEAD to the current git ref (adjust as needed)
          echo "NX_HEAD=${{ github.sha }}" >> $GITHUB_ENV

      - name: Set Up Firebase
        run: npm install -g firebase-tools

      - name: Authenticate to Firebase
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY_PROD_JSON }}

      - name: Use production as environment
        run: npx nx run projects-portless:firebase use production

      - name: Deploy affected services
        timeout-minutes: 20
        uses: mansagroup/nrwl-nx-action@v3
        with:
          targets: deploy
          affected: 'true'
          nxCloud: false
